const { createServer } = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

const dev = process.env.NODE_ENV !== 'production';
const hostname = '0.0.0.0'; // 监听所有网络接口，允许局域网访问
const port = process.env.PORT || 3001;

// 创建Next.js应用实例
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

// HTTPS选项 - 读取SSL证书
const httpsOptions = {
  key: fs.readFileSync(path.join(__dirname, 'certs', 'localhost-key.pem')),
  cert: fs.readFileSync(path.join(__dirname, 'certs', 'localhost.pem')),
};

app.prepare().then(() => {
  // 创建HTTPS服务器
  createServer(httpsOptions, async (req, res) => {
    try {
      // 解析请求URL
      const parsedUrl = parse(req.url, true);
      
      // 处理请求
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('处理请求时出错:', err);
      res.statusCode = 500;
      res.end('内部服务器错误');
    }
  })
  .once('error', (err) => {
    console.error('服务器启动失败:', err);
    process.exit(1);
  })
  .listen(port, () => {
    console.log(`> 准备就绪，HTTPS服务器运行在:`);
    console.log(`  - 本地访问: https://localhost:${port}`);
    console.log(`  - 局域网访问: https://**********:${port}`);
    console.log(`  - 网络访问: https://0.0.0.0:${port}`);
  });
}); 