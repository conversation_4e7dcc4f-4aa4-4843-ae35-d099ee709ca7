name: Deploy to DEV ECS 

on:
  push:
    branches: [ "release" ]

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: sekai-obagent-h5/release

permissions:
  contents: read

jobs:
  build:
    name: Build and Deploy
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4.0.2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_NEW }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_NEW }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2.0.1

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # Build a docker container and
        # push it to ECR so that it can
        # be deployed to ECS.
        docker build --build-arg NODE_ENV=production -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1.3.0
      with:
        task-definition: .ecs/task_definition_prod.json
        container-name: obagent
        image: ${{ steps.build-image.outputs.image }}

    - name: Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1.5.0
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: sekai-h5-obagent-prod
        cluster: sekai-h5-obagent-prod
        wait-for-service-stability: true

    - name: Expose git commit data
      uses: rlespinasse/git-commit-data-action@v1

    # - name: Notify to IM
    #   uses: echoings/actions.notify@v0.1.0
    #   with:
    #     plat_type: 'Lark'
    #     notify_title: 'Dev 环境部署完成'
    #     notify_message: '部署完成，最近的提交: ${{ env.GIT_COMMIT_MESSAGE_SUBJECT }} 作者：${{ env.GIT_COMMIT_COMMITTER_NAME }}  Hash为: ${{ env.GIT_COMMIT_SHORT_SHA }}'
    #   env:
    #     NOTIFY_WEBHOOK: ${{ secrets.NOTIFY_WEBHOOK }}