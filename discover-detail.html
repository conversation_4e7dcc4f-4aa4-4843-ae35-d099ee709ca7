
<div data-layer="运营内容详情页" className="w-96 h-[995px] relative bg-Color-背景色-bg1 overflow-hidden">
    <div data-layer="Detail" className="Detail w-96 left-0 top-0 absolute inline-flex flex-col justify-start items-center gap-4">
      <img data-layer="image" className="Image size-96" src="https://placehold.co/390x390" />
      <div data-layer="image" className="Image size-96 bg-gradient-to-b from-neutral-900/0 to-neutral-900 to 74%" />
      <div data-layer="title" className="Title w-80 flex flex-col justify-start items-start gap-1">
        <div data-layer="Toxic Romance" className="ToxicRomance self-stretch justify-center text-Color-文本-text1 text-3xl font-bold font-['Helvetica_Neue'] leading-loose">Toxic Romance</div>
        <div data-layer="They cheated on you and they took your dog...." className="TheyCheatedOnYouAndTheyTookYourDog self-stretch justify-start text-Color-文本-text2 text-sm font-normal font-['Helvetica_Neue'] leading-none">They cheated on you and they took your dog....</div>
      </div>
      <div data-layer="flow" className="Flow w-96 inline-flex justify-between items-start">
        <div data-layer="left flow" className="LeftFlow w-44 h-[603px] inline-flex flex-col justify-center items-start gap-2">
          <div data-layer="Component 2" data-卡片="Chatbot" data-私密="false" data-私密状态="false" className="Component2 w-44 h-72 relative bg-Color-背景色-bg2 rounded-xl flex flex-col justify-start items-start">
            <div data-layer="Mask group" className="MaskGroup size-44 relative">
              <div data-layer="蒙版" className="size-44 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
              <img data-layer="image" className="Image w-44 h-48 left-0 top-[-13px] absolute rounded-xl" src="https://placehold.co/183x200" />
            </div>
            <div data-layer="Frame 1912055172" className="Frame1912055172 self-stretch px-2 pt-2 flex flex-col justify-start items-start gap-1">
              <div data-layer="Frame 1912055210" className="Frame1912055210 self-stretch inline-flex justify-start items-center gap-1">
                <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-sm font-normal font-['Helvetica_Neue'] leading-none">Xiao</div>
              </div>
              <div data-layer="Chae is autistic and you are her social worker..." className="ChaeIsAutisticAndYouAreHerSocialWorker self-stretch max-h-14 justify-center text-Color-文本-text4 text-xs font-normal font-['Helvetica_Neue'] leading-none">Chae is autistic and you are her social worker...</div>
            </div>
            <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal self-stretch p-2 inline-flex justify-start items-start gap-1 flex-wrap content-start">
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Love</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Genshin</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Game</div>
              </div>
            </div>
            <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[132px] top-[8px] absolute bg-Color-蒙层-Mask60/60 rounded-sm inline-flex justify-end items-center gap-0.5">
              <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
                <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
              </div>
              <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">228</div>
            </div>
          </div>
          <div data-layer="Component 2" data-卡片="story" data-私密="false" data-私密状态="true" className="Component2 w-44 relative bg-Color-背景色-bg2 rounded-xl flex flex-col justify-start items-start">
            <div data-layer="Mask group" className="MaskGroup w-44 h-56 relative">
              <div data-layer="蒙版" className="w-44 h-56 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
              <img data-layer="背景" className="w-44 h-64 left-0 top-0 absolute opacity-80" src="https://placehold.co/183x260" />
              <img data-layer="角色" className="w-56 h-80 left-[200px] top-[327px] absolute origin-top-left rotate-180 rounded-3xl" src="https://placehold.co/218x327" />
            </div>
            <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[139px] top-[8px] absolute bg-Color-蒙层-Mask60/60 rounded-sm inline-flex justify-end items-center gap-0.5">
              <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
                <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
              </div>
              <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">14</div>
            </div>
            <div data-layer="Frame 1912055172" className="Frame1912055172 self-stretch px-2 pt-2 flex flex-col justify-start items-start gap-1">
              <div data-layer="Frame 1912055211" className="Frame1912055211 self-stretch inline-flex justify-start items-center gap-1">
                <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-sm font-normal font-['Helvetica_Neue'] leading-none">Xiao</div>
              </div>
              <div data-layer="Chae is autistic and you are her social worker..." className="ChaeIsAutisticAndYouAreHerSocialWorker self-stretch max-h-14 justify-center text-Color-文本-text4 text-xs font-normal font-['Helvetica_Neue'] leading-none">Chae is autistic and you are her social worker...</div>
            </div>
            <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal self-stretch p-2 inline-flex justify-start items-start gap-1 flex-wrap content-start">
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Love</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Genshin</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Genshin</div>
              </div>
            </div>
            <div data-layer="Story" className="Story size-6 px-1 py-[3px] left-[4px] top-[202px] absolute shadow-[0px_1.5px_1.5px_0px_rgba(0,0,0,0.05)] flex flex-col justify-start items-center gap-0.5">
              <div data-layer="Vector" className="Vector w-5 h-4 bg-Color-文本-text1 outline outline-[0.75px] outline-Color-边框-border5/5" />
            </div>
          </div>
        </div>
        <div data-layer="right flow" className="RightFlow w-44 h-[625px] inline-flex flex-col justify-center items-start gap-2">
          <div data-layer="Component 2" data-卡片="Roleplay" data-私密="false" data-私密状态="true" className="Component2 w-44 h-80 relative bg-Color-背景色-bg2 rounded-xl flex flex-col justify-start items-start">
            <div data-layer="Mask group" className="MaskGroup w-44 h-56 relative">
              <div data-layer="蒙版" className="w-44 h-56 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
              <img data-layer="背景" className="w-44 h-64 left-0 top-0 absolute opacity-80" src="https://placehold.co/183x260" />
              <img data-layer="角色" className="w-56 h-80 left-[200px] top-[327px] absolute origin-top-left rotate-180 rounded-3xl" src="https://placehold.co/218x327" />
            </div>
            <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[139px] top-[8px] absolute bg-Color-蒙层-Mask60/60 rounded-sm inline-flex justify-end items-center gap-0.5">
              <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
                <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
              </div>
              <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">14</div>
            </div>
            <div data-layer="Frame 1912055172" className="Frame1912055172 self-stretch px-2 pt-2 flex flex-col justify-start items-start gap-1">
              <div data-layer="Frame 1912055211" className="Frame1912055211 self-stretch inline-flex justify-start items-center gap-1">
                <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-sm font-normal font-['Helvetica_Neue'] leading-none">Xiao</div>
              </div>
              <div data-layer="Chae is autistic and you are her social worker..." className="ChaeIsAutisticAndYouAreHerSocialWorker self-stretch max-h-14 justify-center text-Color-文本-text4 text-xs font-normal font-['Helvetica_Neue'] leading-none">Chae is autistic and you are her social worker...</div>
            </div>
            <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal self-stretch p-2 inline-flex justify-start items-start gap-1 flex-wrap content-start">
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Love</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Genshin</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Genshin</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Game</div>
              </div>
            </div>
            <div data-layer="Roleplay" className="Roleplay size-6 left-[4px] top-[202px] absolute flex flex-col justify-center items-center gap-1">
              <div data-layer="Rectangle 3" className="Rectangle3 w-[2.62px] h-2 origin-top-left rotate-[-16.04deg] bg-Color-文本-text1 rounded-sm outline outline-[0.75px] outline-Color-边框-border5/5" />
            </div>
          </div>
          <div data-layer="Component 2" data-卡片="Chatbot" data-私密="false" data-私密状态="false" className="Component2 w-44 h-72 relative bg-Color-背景色-bg2 rounded-xl flex flex-col justify-start items-start">
            <div data-layer="Mask group" className="MaskGroup size-44 relative">
              <div data-layer="蒙版" className="size-44 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
              <img data-layer="image" className="Image w-44 h-48 left-0 top-[-13px] absolute rounded-xl" src="https://placehold.co/183x200" />
            </div>
            <div data-layer="Frame 1912055172" className="Frame1912055172 self-stretch px-2 pt-2 flex flex-col justify-start items-start gap-1">
              <div data-layer="Frame 1912055210" className="Frame1912055210 self-stretch inline-flex justify-start items-center gap-1">
                <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-sm font-normal font-['Helvetica_Neue'] leading-none">Xiao</div>
              </div>
              <div data-layer="Chae is autistic and you are her social worker..." className="ChaeIsAutisticAndYouAreHerSocialWorker self-stretch max-h-14 justify-center text-Color-文本-text4 text-xs font-normal font-['Helvetica_Neue'] leading-none">Chae is autistic and you are her social worker...</div>
            </div>
            <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal self-stretch p-2 inline-flex justify-start items-start gap-1 flex-wrap content-start">
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Love</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Genshin</div>
              </div>
              <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- px-1 py-0.5 bg-Color-背景色-bg3 rounded-2xl flex justify-center items-center gap-2">
                <div data-layer="Love" className="Love text-right justify-center text-Color-文本-text3 text-xs font-normal font-['Helvetica_Neue'] leading-none">Game</div>
              </div>
            </div>
            <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[132px] top-[8px] absolute bg-Color-蒙层-Mask60/60 rounded-sm inline-flex justify-end items-center gap-0.5">
              <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
                <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
              </div>
              <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">228</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-layer="Back" className="Back w-2.5 h-4 left-[30px] top-[46px] absolute origin-top-left -rotate-180 bg-Color-文本-text1" />
  </div>