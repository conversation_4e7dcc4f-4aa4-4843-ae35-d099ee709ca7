# HTTPS 本地开发环境设置

本项目已配置支持HTTPS本地开发环境，可以在局域网中访问。

## 🚀 快速开始

### 启动HTTPS开发服务器
```bash
# 启动HTTPS开发服务器
pnpm run dev:https

# 或者启动生产环境HTTPS服务器
pnpm run start:https
```

### 访问地址
- **本地访问**: https://localhost:3001
- **局域网访问**: https://**********:3001
- **其他设备访问**: https://[你的IP地址]:3001

## 📋 配置说明

### SSL证书
- 证书位置: `certs/localhost.pem`
- 私钥位置: `certs/localhost-key.pem`
- 证书有效期: 2027年9月3日
- 支持的域名/IP:
  - localhost
  - 127.0.0.1
  - ::1 (IPv6)
  - ********** (局域网IP)

### 脚本说明
- `pnpm run dev`: 标准HTTP开发服务器 (http://localhost:3001)
- `pnpm run dev:https`: HTTPS开发服务器 (https://localhost:3001)
- `pnpm run start:https`: 生产环境HTTPS服务器

## 🔧 故障排除

### 浏览器安全警告
如果浏览器显示"不安全连接"警告：
1. 点击"高级"
2. 点击"继续访问localhost（不安全）"

### 局域网设备访问
1. 确保防火墙允许3001端口
2. 其他设备需要安装并信任mkcert生成的CA证书
3. 或者在浏览器中手动接受证书

### 重新生成证书
如果需要为不同的IP地址生成证书：
```bash
# 删除旧证书
rm -rf certs/

# 重新生成证书（替换为你的实际IP）
mkdir -p certs
mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 127.0.0.1 ::1 [你的IP地址]
```

## 📱 移动设备访问

### Android设备
1. 在电脑上运行: `mkcert -CAROOT`
2. 将显示的CA证书文件传输到Android设备
3. 在Android设置中安装CA证书

### iOS设备
1. 获取CA证书文件
2. 通过AirDrop或邮件发送到iOS设备
3. 在设置 > 通用 > VPN与设备管理中安装证书
4. 在设置 > 通用 > 关于本机 > 证书信任设置中启用证书

## 🔒 安全注意事项

- 本配置仅用于开发环境
- 生产环境请使用正式的SSL证书
- 不要将证书文件提交到版本控制系统
- 定期更新证书以确保安全性 