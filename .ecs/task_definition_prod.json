{"containerDefinitions": [{"name": "obagent", "image": "010526279899.dkr.ecr.us-east-1.amazonaws.com/tome-tome/release:847d3395a28f1b51e468bc9b96bd96fdab0c8a34", "cpu": 0, "portMappings": [{"name": "3000", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "ANTHROPIC_API_KEY", "value": "************************************************************************************************************"}, {"name": "AWS_ACCESS_KEY_ID", "value": "AKIAQE43KJDNRIKODFEW"}, {"name": "OPENAI_API_KEY", "value": "***************************************************"}, {"name": "OPENAI_BASE_URL", "value": "https://api.openai.com/v1"}, {"name": "PROXY_HOST", "value": "127.0.0.1"}, {"name": "PROXY_PORT", "value": "7890"}, {"name": "USE_PROXY", "value": "false"}, {"name": "NEXT_PUBLIC_ACCESS_TOKEN", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1MywidXNlcl9uYW1lIjoiTWVtZUxvcmQxNyIsImlhdCI6MTc0ODU4Mzg4OSwiZXhwIjoxNzQ5MTg4Njg5fQ==.xjOqnPDIbsah5ET1CVqZe6D1MHB5A+T8poSvBKT055s="}, {"name": "NEXT_PUBLIC_STG_HOST", "value": "http://develop-api.sekai.chat"}, {"name": "NEXT_PUBLIC_PRD_HOST", "value": "https://api.sekai.chat"}, {"name": "NEXT_PUBLIC_NODE_ENV", "value": "production"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/sekai-h5-obagent-prod", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "sekai-h5-obagent-prod", "taskRoleArn": "arn:aws:iam::010526279899:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::010526279899:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "8192", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": []}