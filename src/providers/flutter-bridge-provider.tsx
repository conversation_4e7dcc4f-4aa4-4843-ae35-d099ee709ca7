'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// 声明全局的flutter_inappwebview对象
declare global {
  interface Window {
    flutter_inappwebview?: {
      callHandler: (handlerName: string, ...args: unknown[]) => void;
    };
    receiveMessageFromFlutter?: (message: string) => void;
  }
}

interface FlutterBridgeContextType {
  isFlutterAvailable: boolean;
  receivedMessages: string[];
  sendMessageToFlutter: (message: string) => void;
  clearReceivedMessages: () => void;
}

const FlutterBridgeContext = createContext<FlutterBridgeContextType | undefined>(undefined);

export function useFlutterBridge() {
  const context = useContext(FlutterBridgeContext);
  if (context === undefined) {
    throw new Error('useFlutterBridge must be used within a FlutterBridgeProvider');
  }
  return context;
}

interface FlutterBridgeProviderProps {
  children: ReactNode;
}

export function FlutterBridgeProvider({ children }: FlutterBridgeProviderProps) {
  const [isFlutterAvailable, setIsFlutterAvailable] = useState(false);
  const [receivedMessages, setReceivedMessages] = useState<string[]>([]);

  useEffect(() => {
    // 检查Flutter WebView是否可用
    const checkFlutterAvailability = () => {
      if (window.flutter_inappwebview) {
        setIsFlutterAvailable(true);
        console.log('Flutter WebView bridge is available');
      } else {
        setIsFlutterAvailable(false);
        console.log('Flutter WebView bridge is not available');
      }
    };

    // 定义接收Flutter消息的全局函数
    window.receiveMessageFromFlutter = (message: string) => {
      console.log('Received message from Flutter:', message);
      setReceivedMessages(prev => [...prev, message]);
    };

    // 初始检查
    checkFlutterAvailability();

    // 定期检查Flutter可用性
    const interval = setInterval(checkFlutterAvailability, 1000);

    return () => {
      clearInterval(interval);
      // 清理全局函数
      if (window.receiveMessageFromFlutter) {
        window.receiveMessageFromFlutter = undefined;
      }
    };
  }, []);

  const sendMessageToFlutter = (message: string) => {
    if (!message.trim()) {
      throw new Error('消息内容不能为空');
    }

    if (!window.flutter_inappwebview) {
      throw new Error('Flutter WebView bridge 不可用');
    }

    try {
      // 发送消息到Flutter
      window.flutter_inappwebview.callHandler('messageHandler', message);
      console.log('Sent message to Flutter:', message);
    } catch (error) {
      console.error('Error sending message to Flutter:', error);
      throw error;
    }
  };

  const clearReceivedMessages = () => {
    setReceivedMessages([]);
  };

  const value = {
    isFlutterAvailable,
    receivedMessages,
    sendMessageToFlutter,
    clearReceivedMessages,
  };

  return (
    <FlutterBridgeContext.Provider value={value}>
      {children}
    </FlutterBridgeContext.Provider>
  );
} 