// app/providers.tsx
'use client'

import posthog from 'posthog-js'
import { PostHogProvider as PHProvider } from 'posthog-js/react'
import { useEffect } from 'react'

export function PostHogProvider({ children }: { children: React.ReactNode }) {
    useEffect(() => {
      const posthogKey = process.env.NEXT_PUBLIC_POSTHOG_KEY
      const posthogHost = process.env.NEXT_PUBLIC_POSTHOG_HOST
      
      if (!posthogKey || !posthogHost) {
        console.warn('PostHog configuration missing')
        return
      }
      
      posthog.init(posthogKey, {
        api_host: posthogHost,
        
        // 页面浏览采集设置
        capture_pageview: 'history_change', // 或者设为 false 来禁用自动页面浏览采集
        capture_pageleave: true, // 是否采集页面离开事件，可以设为false
        
        // 禁用自动事件捕获（点击、表单提交等）
        autocapture: false, // 禁用自动事件捕获，减少网络请求
        
        // 会话录制设置
        disable_session_recording: true, // 禁用会话录制
        
        // 其他设置
        cross_subdomain_cookie: false,
        persistence: 'localStorage+cookie', // 或者使用 'memory' 来减少存储
        
        // 调试模式（生产环境建议关闭）
        debug: process.env.NODE_ENV === 'development',
        
        // 禁用其他功能
        disable_surveys: true, // 禁用问卷调查
        property_denylist: [], // 黑名单属性，不采集某些属性
      })
  }, [])

  return (
    <PHProvider client={posthog}>
      {children}
    </PHProvider>
  )
} 