import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowRight, Smartphone } from 'lucide-react';

export default function Home() {
  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">H5 Discover Page</h1>
        <p className="text-lg text-muted-foreground">
          Flutter 桥接通信测试平台
        </p>
      </div>

      <div className="flex justify-center">
        <Card className="hover:shadow-lg transition-shadow max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="w-6 h-6" />
              Flutter Bridge 测试
            </CardTitle>
            <CardDescription>
              测试 H5 与 Flutter 应用之间的通信桥接
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              支持双向消息通信，可以发送消息到 Flutter 应用，也可以接收来自 Flutter 的消息。
            </p>
            <Link href="/flutter-bridge">
              <Button className="w-full">
                进入测试
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>项目说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h3 className="font-semibold mb-1">技术栈</h3>
              <p className="text-muted-foreground">
                Next.js 15 + TypeScript + Tailwind CSS + React Context
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-1">Flutter Bridge</h3>
              <p className="text-muted-foreground">
                通过 JavaScript 接口与 Flutter WebView 进行双向通信，使用 React Context 提供全局状态管理。
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-1">通信机制</h3>
              <p className="text-muted-foreground">
                H5 → Flutter: 使用 window.flutter_inappwebview.callHandler()<br/>
                Flutter → H5: 调用全局函数 receiveMessageFromFlutter()
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 