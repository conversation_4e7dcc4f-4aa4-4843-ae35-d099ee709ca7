import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { PostHogProvider } from "@/providers/posthog-provider";
import { FlutterBridgeProvider } from "@/providers/flutter-bridge-provider";
import { Toaster } from "sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "H5 Discover Page",
  description: "Flutter Bridge & PostHog Demo",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <PostHogProvider>
          <FlutterBridgeProvider>
            {children}
            <Toaster position="top-center" />
          </FlutterBridgeProvider>
        </PostHogProvider>
      </body>
    </html>
  );
} 