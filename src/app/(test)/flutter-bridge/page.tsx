'use client';

import { useState, useEffect } from 'react';
import { useFlutterBridge } from '@/providers/flutter-bridge-provider';

export default function FlutterBridgeDemoPage() {
  const [messageInput, setMessageInput] = useState('');
  const [sentMessages, setSentMessages] = useState<string[]>([]);
  
  const {
    isFlutterAvailable,
    receivedMessages,
    sendMessageToFlutter,
    clearReceivedMessages,
  } = useFlutterBridge();

  useEffect(() => {
    // 保存原始背景色
    const originalBodyBackground = document.body.style.backgroundColor;
    const originalDocumentElementBackground = document.documentElement.style.backgroundColor;

    // 设置新的背景色
    document.body.style.backgroundColor = 'white';
    document.documentElement.style.backgroundColor = 'white';

    // 组件卸载时恢复原始背景色
    return () => {
      document.body.style.backgroundColor = originalBodyBackground;
      document.documentElement.style.backgroundColor = originalDocumentElementBackground;
    };
  }, []); // 空依赖数组确保 effect 只在挂载和卸载时运行

  const handleSendMessage = () => {
    if (!messageInput.trim()) {
      alert('请输入消息内容');
      return;
    }

    try {
      sendMessageToFlutter(messageInput);
      
      // 记录发送的消息
      setSentMessages(prev => [...prev, messageInput]); 
      
      // 清空输入框
      setMessageInput('');
    } catch (error) {
      console.error('Error sending message to Flutter:', error);
      alert('发送消息失败: ' + error);
    }
  };

  const sendTestMessage = (testMessage: string) => {
    setMessageInput(testMessage);
  };

  const clearMessages = () => {
    clearReceivedMessages();
    setSentMessages([]);
  };

  return (
    <div className="relative min-h-screen bg-white p-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
          Flutter JSBridge 通信测试
        </h1>

        {/* 连接状态 */}
        <div className="mb-6 p-4 rounded-lg border">
          <h2 className="text-lg font-semibold mb-2">连接状态</h2>
          <div className="flex items-center gap-2">
            <div 
              className={`w-3 h-3 rounded-full ${
                isFlutterAvailable ? 'bg-green-500' : 'bg-red-500'
              }`}
            />
            <span className={isFlutterAvailable ? 'text-green-600' : 'text-red-600'}>
              {isFlutterAvailable ? 'Flutter WebView 已连接' : 'Flutter WebView 未连接'}
            </span>
          </div>
        </div>

        {/* 发送消息区域 */}
        <div className="mb-6 p-4 bg-white rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">发送消息到 Flutter</h2>
          
          <div className="flex gap-2 mb-4">
            <input
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              placeholder="输入要发送的消息..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            />
            <button
              onClick={handleSendMessage}
              disabled={!isFlutterAvailable}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              发送
            </button>
          </div>

          {/* 快速测试按钮 */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => sendTestMessage(JSON.stringify({"action":"pageReady"}))}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300"
            >
              pageReady
            </button>
            <button
              onClick={() => sendTestMessage(JSON.stringify({"action":"openUrl","url":"/unSearchPage"}))}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300"
            >
              go to unSearchPage
            </button>
            <button
              onClick={() => sendTestMessage(JSON.stringify({"action":"openUrl","url":"/unDefaultSearchPage"}))}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300"
            >
              go to unDefaultSearchPage
            </button>
          </div>
        </div>

        {/* 消息历史 */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* 已发送消息 */}
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-blue-600">已发送消息</h3>
              <span className="text-sm text-gray-500">({sentMessages.length})</span>
            </div>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {sentMessages.length === 0 ? (
                <p className="text-gray-500 text-sm">暂无发送记录</p>
              ) : (
                sentMessages.map((message, index) => (
                  <div key={index} className="p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                    <p className="text-sm">{message}</p>
                    <span className="text-xs text-gray-500">
                      {new Date().toLocaleTimeString()}
                    </span>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 接收到的消息 */}
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-green-600">接收到的消息</h3>
              <span className="text-sm text-gray-500">({receivedMessages.length})</span>
            </div>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {receivedMessages.length === 0 ? (
                <p className="text-gray-500 text-sm">暂无接收记录</p>
              ) : (
                receivedMessages.map((message, index) => (
                  <div key={index} className="p-2 bg-green-50 rounded border-l-4 border-green-400">
                    <p className="text-sm">{typeof message === 'object' ? JSON.stringify(message, null, 2) : message}</p>
                    <span className="text-xs text-gray-500">
                      {new Date().toLocaleTimeString()}
                    </span>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="mt-6 flex justify-center gap-4">
          <button
            onClick={clearMessages}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            清空消息记录
          </button>
        </div>

        {/* 使用说明 */}
        <div className="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <h3 className="text-lg font-semibold mb-2 text-yellow-800">使用说明</h3>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>发送消息到Flutter:</strong> 使用 window.flutter_inappwebview.callHandler(&apos;messageHandler&apos;, &apos;message&apos;)</p>
            <p><strong>接收Flutter消息:</strong> Flutter调用 receiveMessageFromFlutter(message) 函数</p>
            <p><strong>Flutter端代码示例:</strong></p>
            <pre className="bg-yellow-100 p-2 rounded text-xs overflow-x-auto">
{`_webViewController!.evaluateJavascript(
  source: "receiveMessageFromFlutter('$\{message.replaceAll("'", "\\\\'")}');",
)`}
            </pre>
            <p><strong>全局使用:</strong> 现在可以在应用的任何页面使用 useFlutterBridge() hook</p>
          </div>
        </div>
      </div>
    </div>
  );
} 