import Link from 'next/link';
import React from 'react';
import BannerCarousel from '@/components/features/discover/BannerCarousel';

const DiscoverDevPage = () => {
  const sampleDetailId = 'sample-detail-id';

  return (
    <div className="p-8 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold mb-8 text-gray-800">Discover Pages (Dev Test)</h1>

      <nav>
        <ul className="space-y-4">
          <li>
            <Link href="/discover" className="text-blue-600 hover:text-blue-800 hover:underline text-xl p-3 bg-white rounded-lg shadow transition-all duration-200 ease-in-out inline-block">
                Go to Discover Page (/discover)
            </Link>
          </li>
          <li>
            <Link href={`/discover/detail/${sampleDetailId}`} className="text-green-600 hover:text-green-800 hover:underline text-xl p-3 bg-white rounded-lg shadow transition-all duration-200 ease-in-out inline-block">
                Go to Detail Page (/discover/detail/{sampleDetailId})
            </Link>
          </li>
        </ul>
      </nav>

      <div className="mt-12">
        <h2 className="text-2xl font-semibold mb-4 text-gray-700">Banner Carousel Preview:</h2>
        <div
          className="max-w-3xl mx-auto"
          // style={{ border: '5px solid green', padding: '10px' }} /* DEBUG: REMOVING Green border */
        > {/* Constrain width for better preview */}
          <BannerCarousel />
        </div>
      </div>

      <div className="mt-12 p-6 bg-white rounded-lg shadow">
        <h2 className="text-2xl font-semibold mb-4 text-gray-700">Notes:</h2>
        <ul className="list-disc list-inside text-gray-600 space-y-2">
          <li>This page is for development and testing purposes.</li>
          <li>It provides quick links to the newly created Discover and Detail pages.</li>
          <li>The Detail Page link uses a sample ID: <code className="bg-gray-200 px-1 rounded">{sampleDetailId}</code>.</li>
        </ul>
      </div>
    </div>
  );
};

export default DiscoverDevPage;