import React from 'react';

const DiscoverPage = () => {
  return (
    <div className="bg-black text-white min-h-screen p-4">
      <header className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Discover</h1>
        {/* Search Icon Placeholder */}
        <div className="w-6 h-6 bg-gray-700 rounded-full"></div>
      </header>

      {/* Banner Placeholder */}
      <section className="mb-8">
        <div className="bg-gray-800 aspect-[16/9] rounded-lg flex items-center justify-center">
          <p className="text-gray-500">Banner Placeholder</p>
        </div>
      </section>

      {/* Fandom Section Placeholder */}
      <section className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">🎭 Fandom</h2>
          <div className="w-4 h-4 bg-gray-700 rounded-sm"></div> {/* Arrow Placeholder */}
        </div>
        <div className="flex space-x-4 overflow-x-auto pb-2">
          {/* Card Placeholders */}
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-gray-800 w-40 h-24 rounded-lg flex-shrink-0 flex items-center justify-center">
              <p className="text-gray-500">Fandom Card {i}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Your Toxic Ex Section Placeholder */}
      <section className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Your Toxic Ex</h2>
          <div className="w-4 h-4 bg-gray-700 rounded-sm"></div> {/* Arrow Placeholder */}
        </div>
        <div className="grid grid-cols-2 gap-4">
          {/* Card Placeholders */}
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-gray-800 aspect-square rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Toxic Ex Card {i}</p>
            </div>
          ))}
        </div>
      </section>

       {/* Hot Guys Only Section Placeholder (往期运营内容) */}
      <section>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Hot Guys Only</h2>
          <div className="w-4 h-4 bg-gray-700 rounded-sm"></div> {/* Arrow Placeholder */}
        </div>
        <div className="grid grid-cols-2 gap-4">
          {/* Card Placeholders */}
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-gray-800 aspect-square rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Hot Guy Card {i}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default DiscoverPage;