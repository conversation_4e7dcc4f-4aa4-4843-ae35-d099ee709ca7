import React from 'react';

type DetailPageProps = {
  params: { id: string };
};

const DetailPage = ({ params }: DetailPageProps) => {
  const { id } = params;

  return (
    <div className="bg-black text-white min-h-screen p-4">
      <header className="flex items-center mb-6">
        {/* Back Button Placeholder */}
        <div className="w-6 h-6 bg-gray-700 rounded-full mr-4"></div>
        <h1 className="text-xl font-semibold">运营内容详情页</h1>
      </header>

      {/* Banner Placeholder */}
      <section className="mb-8">
        <div className="bg-gray-800 aspect-[16/9] rounded-lg flex items-center justify-center">
          <p className="text-gray-500">Detail Banner Placeholder (ID: {id})</p>
        </div>
      </section>

      {/* Content Title & Description Placeholder */}
      <section className="mb-8">
        <h2 className="text-2xl font-bold mb-2">Toxic Romance</h2>
        <p className="text-gray-400 text-sm">They cheated on you and they took your dog....</p>
      </section>

      {/* Related Content Grid Placeholder */}
      <section>
        <div className="grid grid-cols-2 gap-4">
          {/* Card Placeholders */}
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="bg-gray-800 aspect-square rounded-lg flex flex-col items-center justify-center p-2">
              <div className="w-full aspect-[3/4] bg-gray-700 rounded mb-2"></div> {/* Image Placeholder */}
              <p className="text-gray-500 text-sm text-center">Related Item {i}</p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default DetailPage;