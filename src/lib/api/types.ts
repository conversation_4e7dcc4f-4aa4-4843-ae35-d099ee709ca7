// --- Sekai Info ---
interface SekaiCategory {
  id: number;
  label_name: string;
  display_name: string;
  emoji: string;
}

export interface SekaiItem {
  sekaiId: number;
  newCoverUrl: string;
  creatorUserId: number;
  creatorUserName: string;
  title: string;
  isPublic: boolean;
  intro: string;
  category: SekaiCategory[];
  viewCount: number;
  templateId: number;
  coverVideo: string;
  multiPlayerCoverflag: boolean;
}

export interface SekaiInfoResponse {
  code: number;
  success: boolean;
  data: SekaiItem[];
  message: string;
}

// --- Banner & Campaign ---
interface CampaignBannerData {
  title: string;
  subtitle: string;
  cover_image_url: string;
  secondary_page_image_url: string;
  linked_sekai_ids: number[];
}

export interface CampaignBannerItem {
  id: number;
  type: 'sekai_campaign_banner' | 'sekai_campaign_past';
  data: CampaignBannerData;
  status: string;
  sort_order: number;
  created_at: number;
  updated_at: number;
}

export interface CampaignBannerResponseData {
  total: number;
  page: number;
  size: number;
  pages: number;
  items: CampaignBannerItem[];
}

export interface CampaignBannerResponse {
  code: number;
  msg: string;
  data: CampaignBannerResponseData;
}

// --- Discover Page Banner ---
export interface DiscoverBannerItem {
  id: string; // Can be number or string depending on API
  imageUrl: string;
  title?: string; // Text overlay on the banner
  linkUrl?: string; // URL to navigate to on click (e.g., /discover/detail/{id} or external)
  altText?: string; // Accessibility text for the image
}


// --- Fandom Info ---
interface FandomExtra {
  backgroundColor: number;
  charCount: number;
  charPose: string;
  coverUrl?: string;
  description?: string;
  weight?: number;
}

export interface FandomItem {
  id: number;
  displayName: string;
  tagType: number;
  creationType: number;
  extra: FandomExtra;
}

export interface FandomResponseData {
  items: FandomItem[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface FandomResponse {
  code: number;
  success: boolean;
  data: FandomResponseData;
  message: string;
}