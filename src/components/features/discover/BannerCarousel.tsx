'use client';

import React, { useState } from 'react'; // Removed useEffect, use<PERSON><PERSON>back as <PERSON>wi<PERSON> handles autoplay
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils/cn';
import type { DiscoverBannerItem } from '@/lib/api/types';

// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperClass } from 'swiper/types'; // For Swiper instance type

// Import Swiper modules
import { Autoplay, EffectFade } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/effect-fade';

// 模拟 API 返回的 Banner 数据
const mockBanners: DiscoverBannerItem[] = [
  {
    id: '1',
    imageUrl: 'https://stage-data.sekai.chat/aiu-audio/test/hfZ6QtNo0puiIz8AUGys.png',
    title: 'Toxic Romance: Can you handle their extreme love?',
    linkUrl: '/discover/detail/1',
    altText: 'Banner image 1 featuring characters from Toxic Romance',
  },
  {
    id: '2',
    imageUrl: 'https://stage-data.sekai.chat/aiu-audio/test/C2xWu9skzYxfb8UNIam8.png',
    title: 'New Adventure Awaits!',
    linkUrl: '/discover/detail/2',
    altText: 'Banner image 2 announcing a new adventure',
  },
  {
    id: '3',
    imageUrl: 'https://stage-data.sekai.chat/aiu-audio/test/BnNgHTSIo7eiktokIvPD.png',
    title: 'Discover Hidden Gems',
    linkUrl: '/discover/detail/3',
    altText: 'Banner image 3 showcasing hidden gems',
  },
  {
    id: '4',
    imageUrl: 'https://stage-data.sekai.chat/aiu-audio/test/j9KZaTQoUCsriO6Qr6Lq.png',
    title: 'Limited Time Offer!',
    linkUrl: '/discover/detail/4',
    altText: 'Banner image 4 for a limited time offer',
  },
];

interface BannerCarouselProps {
  banners?: DiscoverBannerItem[];
  autoplayInterval?: number; // 自动播放间隔，单位毫秒
}

const BannerCarousel: React.FC<BannerCarouselProps> = ({
  banners = mockBanners,
  autoplayInterval = 5000,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [swiperInstance, setSwiperInstance] = useState<SwiperClass | null>(null);
  const router = useRouter();

  const handleDotClick = (index: number) => {
    if (swiperInstance) {
      swiperInstance.slideToLoop(index); 
    }
  };

  const handleBannerClick = (banner: DiscoverBannerItem) => {
    if (banner.linkUrl) {
      router.push(banner.linkUrl);
    }
  };

  if (!banners || banners.length === 0) {
    return (
      <div className="w-full aspect-[16/7] bg-gray-200 rounded-xl flex items-center justify-center">
        <p className="text-gray-500">No banners to display.</p>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col items-center"> {/* Overall component wrapper */}
      {/* Banner Image Area */}
      <div
        className="w-full overflow-hidden rounded-xl shadow-lg"
        data-testid="banner-carousel-root"
        style={{
          position: 'relative', 
          height: '384px !important', 
          zIndex: 1,
          display: 'block !important',
        }}
      >
        <Swiper
          modules={[Autoplay, EffectFade]}
          effect="fade"
          fadeEffect={{ crossFade: true }}
          autoplay={ banners.length > 1 ? { delay: autoplayInterval, disableOnInteraction: false } : false }
          loop={banners.length > 1} // Only loop if more than one banner
          slidesPerView={1}
          grabCursor={true}
          onSwiper={setSwiperInstance}
          onSlideChange={(swiper) => setCurrentIndex(swiper.realIndex)}
          className="w-full h-full" // Ensure Swiper container itself has full height
        >
          {banners.map((banner, index) => (
            <SwiperSlide
              key={banner.id}
              onClick={() => handleBannerClick(banner)}
              className="cursor-pointer"
              style={{ height: '384px' }} // Apply fixed height directly to SwiperSlide
            >
              <div style={{ position: 'relative', width: '100%', height: '100%' }}> {/* This should now be 100% of 384px */}
                <Image
                  src={banner.imageUrl}
                  alt={banner.altText || `Banner ${index + 1}`}
                  fill={true}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 60vw"
                  priority={index === 0}
                  className="object-cover"
                />
                {/* Gradient overlay */}
                <div className="absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-black/60 via-black/30 to-transparent rounded-b-xl" />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Scoped JSX styles for box-sizing fix */}
        <style jsx>{`
          [data-testid="banner-carousel-root"] {
            box-sizing: border-box !important;
          }
          /* Ensure Swiper slides are also border-box if they have padding/border */
          /* It's generally good practice for Swiper slides to be box-sizing: border-box */
          .swiper-slide {
            box-sizing: border-box !important;
            /* position: relative !important; */ /* Moved to inner div */
            /* height: 100% !important; */ /* Moved to inner div */
          }
        `}</style>
        
        {/* Custom Navigation Dots Area */}
        {banners.length > 1 && (
          <div
            className="space-x-1.5" // Tailwind class for spacing between dots
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'absolute',
              left: '0',
              right: '0',
              width: '100%',
              bottom: '12px', // Adjust as needed
              zIndex: 20, 
            }}
          >
            {banners.map((_, index) => (
              <button
                key={index}
                onClick={() => handleDotClick(index)}
                aria-label={`Go to slide ${index + 1}`}
                className={cn(
                  'h-1.5 rounded-sm transition-all duration-300 ease-in-out',
                  currentIndex === index
                    ? 'w-7 bg-white' // Active dot style
                    : 'w-3 bg-white/40 hover:bg-white/60' // Inactive dot style
                )}
              />
            ))}
          </div>
        )}
      </div> {/* End of Banner Image Area */}
    </div>
  );
};

export default BannerCarousel;