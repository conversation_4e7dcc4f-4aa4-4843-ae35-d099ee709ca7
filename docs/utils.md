# 🛠️ 工具函数

项目中的通用工具函数库，提供常用的辅助功能。

## 样式工具

### 类名合并 (`src/lib/utils/cn.ts`)

使用 `clsx` 和 `tailwind-merge` 实现智能的类名合并。

```typescript
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

#### 使用示例

```tsx
// 基础使用
<div className={cn("px-4 py-2", "bg-blue-500")} />
// 输出: "px-4 py-2 bg-blue-500"

// 条件类名
<div className={cn(
  "px-4 py-2",
  isActive && "bg-blue-500",
  isDisabled && "opacity-50 cursor-not-allowed"
)} />

// 覆盖冲突的类名
<div className={cn("px-4", "px-6")} />
// 输出: "px-6" (tailwind-merge 会智能处理冲突)

// 对象语法
<div className={cn({
  "bg-blue-500": isBlue,
  "bg-red-500": isRed,
  "font-bold": isBold,
})} />

// 数组语法
const classes = ["px-4", "py-2"]
<div className={cn(...classes, "bg-white")} />
```

## 数据处理

### JSON 解析器 (`src/lib/utils/json-parser.ts`)

安全的 JSON 解析和序列化工具。

```typescript
interface ParseResult<T> {
  success: boolean
  data?: T
  error?: string
}

export function safeJsonParse<T = any>(json: string): ParseResult<T> {
  try {
    const data = JSON.parse(json)
    return { success: true, data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'JSON 解析失败'
    }
  }
}

export function safeJsonStringify(data: any, space?: number): string {
  try {
    return JSON.stringify(data, null, space)
  } catch (error) {
    console.error('JSON 序列化失败:', error)
    return '{}'
  }
}

// 深度解析 JSON（处理嵌套的 JSON 字符串）
export function deepJsonParse(data: any): any {
  if (typeof data === 'string') {
    const result = safeJsonParse(data)
    if (result.success) {
      return deepJsonParse(result.data)
    }
  } else if (Array.isArray(data)) {
    return data.map(deepJsonParse)
  } else if (data && typeof data === 'object') {
    const parsed: any = {}
    for (const key in data) {
      parsed[key] = deepJsonParse(data[key])
    }
    return parsed
  }
  return data
}
```

### 数据验证 (`src/lib/utils/validators.ts`)

常用的数据验证函数。

```typescript
// 邮箱验证
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 手机号验证（中国大陆）
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// URL 验证
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// 身份证号验证（中国大陆）
export function isValidIdCard(idCard: string): boolean {
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
  if (!idCardRegex.test(idCard)) return false
  
  // 校验码验证
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
  
  let sum = 0
  for (let i = 0; i < 17; i++) {
    sum += parseInt(idCard[i]) * weights[i]
  }
  
  const checkCode = checkCodes[sum % 11]
  return idCard[17].toUpperCase() === checkCode
}

// 密码强度验证
export interface PasswordStrength {
  score: number // 0-4
  feedback: string[]
}

export function checkPasswordStrength(password: string): PasswordStrength {
  const feedback: string[] = []
  let score = 0
  
  if (password.length >= 8) score++
  else feedback.push('密码长度至少8位')
  
  if (/[a-z]/.test(password)) score++
  else feedback.push('包含小写字母')
  
  if (/[A-Z]/.test(password)) score++
  else feedback.push('包含大写字母')
  
  if (/\d/.test(password)) score++
  else feedback.push('包含数字')
  
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++
  else feedback.push('包含特殊字符')
  
  return { score: Math.min(score, 4), feedback }
}
```

## 格式化工具

### 日期格式化 (`src/lib/utils/date.ts`)

```typescript
// 格式化日期
export function formatDate(
  date: Date | string | number,
  format: string = 'YYYY-MM-DD'
): string {
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return 'Invalid Date'
  }
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 相对时间
export function getRelativeTime(date: Date | string | number): string {
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  if (seconds > 0) return `${seconds}秒前`
  return '刚刚'
}

// 日期范围
export function getDateRange(type: 'today' | 'week' | 'month' | 'year') {
  const now = new Date()
  const start = new Date()
  const end = new Date()
  
  switch (type) {
    case 'today':
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      break
    case 'week':
      start.setDate(now.getDate() - now.getDay())
      start.setHours(0, 0, 0, 0)
      end.setDate(start.getDate() + 6)
      end.setHours(23, 59, 59, 999)
      break
    case 'month':
      start.setDate(1)
      start.setHours(0, 0, 0, 0)
      end.setMonth(now.getMonth() + 1, 0)
      end.setHours(23, 59, 59, 999)
      break
    case 'year':
      start.setMonth(0, 1)
      start.setHours(0, 0, 0, 0)
      end.setMonth(11, 31)
      end.setHours(23, 59, 59, 999)
      break
  }
  
  return { start, end }
}
```

### 数字格式化 (`src/lib/utils/number.ts`)

```typescript
// 千分位格式化
export function formatThousands(num: number | string): string {
  const n = typeof num === 'string' ? parseFloat(num) : num
  return n.toLocaleString('zh-CN')
}

// 货币格式化
export function formatCurrency(
  amount: number,
  currency: string = 'CNY',
  locale: string = 'zh-CN'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount)
}

// 文件大小格式化
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// 百分比格式化
export function formatPercent(
  value: number,
  decimals: number = 2
): string {
  return `${(value * 100).toFixed(decimals)}%`
}

// 数字缩写
export function abbreviateNumber(num: number): string {
  if (num < 1000) return String(num)
  if (num < 10000) return `${(num / 1000).toFixed(1)}K`
  if (num < 1000000) return `${Math.floor(num / 1000)}K`
  if (num < 10000000) return `${(num / 1000000).toFixed(1)}M`
  return `${Math.floor(num / 1000000)}M`
}
```

## 字符串处理

### 字符串工具 (`src/lib/utils/string.ts`)

```typescript
// 截断字符串
export function truncate(str: string, length: number, suffix = '...'): string {
  if (str.length <= length) return str
  return str.slice(0, length - suffix.length) + suffix
}

// 首字母大写
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

// 驼峰转短横线
export function kebabCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase()
}

// 短横线转驼峰
export function camelCase(str: string): string {
  return str
    .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
    .replace(/^[A-Z]/, (letter) => letter.toLowerCase())
}

// 生成随机字符串
export function randomString(length: number, chars?: string): string {
  const defaultChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charSet = chars || defaultChars
  let result = ''
  
  for (let i = 0; i < length; i++) {
    result += charSet.charAt(Math.floor(Math.random() * charSet.length))
  }
  
  return result
}

// 提取 URL 参数
export function getUrlParams(url: string): Record<string, string> {
  const params: Record<string, string> = {}
  const urlObj = new URL(url)
  
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value
  })
  
  return params
}
```

## 数组和对象工具

### 数组工具 (`src/lib/utils/array.ts`)

```typescript
// 数组去重
export function unique<T>(arr: T[]): T[] {
  return Array.from(new Set(arr))
}

// 数组分组
export function groupBy<T>(
  arr: T[],
  key: keyof T | ((item: T) => string)
): Record<string, T[]> {
  return arr.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : String(item[key])
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

// 数组分块
export function chunk<T>(arr: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size))
  }
  return chunks
}

// 打乱数组
export function shuffle<T>(arr: T[]): T[] {
  const shuffled = [...arr]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}
```

### 对象工具 (`src/lib/utils/object.ts`)

```typescript
// 深拷贝
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as any
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as any
  
  const cloned = {} as T
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  return cloned
}

// 对象选择器
export function pick<T, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}

// 对象排除器
export function omit<T, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj }
  keys.forEach(key => {
    delete result[key]
  })
  return result
}

// 深度合并
export function deepMerge<T extends object>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target
  
  const source = sources.shift()
  if (!source) return target
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key]
      const targetValue = target[key]
      
      if (isObject(targetValue) && isObject(sourceValue)) {
        target[key] = deepMerge(targetValue, sourceValue)
      } else {
        target[key] = sourceValue as T[Extract<keyof T, string>]
      }
    }
  }
  
  return deepMerge(target, ...sources)
}

function isObject(item: any): item is object {
  return item && typeof item === 'object' && !Array.isArray(item)
}
```

## 浏览器工具

### 存储工具 (`src/lib/utils/storage.ts`)

```typescript
// 安全的 localStorage 操作
export const storage = {
  get<T>(key: string, defaultValue?: T): T | undefined {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch {
      return defaultValue
    }
  },
  
  set(key: string, value: any): void {
    try {
      window.localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('localStorage set error:', error)
    }
  },
  
  remove(key: string): void {
    try {
      window.localStorage.removeItem(key)
    } catch (error) {
      console.error('localStorage remove error:', error)
    }
  },
  
  clear(): void {
    try {
      window.localStorage.clear()
    } catch (error) {
      console.error('localStorage clear error:', error)
    }
  }
}

// Cookie 操作
export const cookie = {
  get(name: string): string | null {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null
    }
    return null
  },
  
  set(name: string, value: string, days?: number): void {
    let expires = ''
    if (days) {
      const date = new Date()
      date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
      expires = `; expires=${date.toUTCString()}`
    }
    document.cookie = `${name}=${value}${expires}; path=/`
  },
  
  remove(name: string): void {
    document.cookie = `${name}=; Max-Age=-99999999;`
  }
}
```

## 性能工具

### 函数节流和防抖 (`src/lib/utils/performance.ts`)

```typescript
// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function (...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return function (...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// 缓存函数结果
export function memoize<T extends (...args: any[]) => any>(
  func: T
): T {
  const cache = new Map()
  
  return ((...args: Parameters<T>) => {
    const key = JSON.stringify(args)
    if (cache.has(key)) {
      return cache.get(key)
    }
    const result = func(...args)
    cache.set(key, result)
    return result
  }) as T
}
```

## 最佳实践

1. **纯函数** - 工具函数应该是纯函数，不产生副作用
2. **类型安全** - 使用 TypeScript 提供完整的类型定义
3. **错误处理** - 对可能出错的操作进行异常处理
4. **性能考虑** - 避免不必要的计算和内存占用
5. **单一职责** - 每个函数只做一件事
6. **命名清晰** - 函数名应该清楚地表达其功能
7. **文档完善** - 为复杂的函数提供使用示例 