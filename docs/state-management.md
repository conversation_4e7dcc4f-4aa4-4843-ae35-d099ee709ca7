# 💾 状态管理系统

使用 Zustand 实现的轻量级状态管理方案。

## 核心特性

- **轻量级** - 仅 2KB gzipped
- **TypeScript 支持** - 完整的类型推导
- **无需 Provider** - 直接使用 Hook
- **持久化支持** - 内置 localStorage 持久化
- **DevTools 集成** - 支持 Redux DevTools

## 基础用法

### 创建 Store (`src/stores/app-store.ts`)

```typescript
import { create } from "zustand"
import { persist } from "zustand/middleware"

interface AppState {
  // 状态定义
  theme: 'light' | 'dark' | 'system'
  user: User | null
  isLoading: boolean
  
  // 操作方法
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  
  // 异步操作
  initialize: () => Promise<void>
  reset: () => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      theme: 'system',
      user: null,
      isLoading: false,
      
      // 同步操作
      setTheme: (theme) => set({ theme }),
      setUser: (user) => set({ user }),
      setLoading: (isLoading) => set({ isLoading }),
      
      // 异步操作
      initialize: async () => {
        set({ isLoading: true })
        try {
          // 初始化逻辑
          const user = await fetchCurrentUser()
          set({ user })
        } catch (error) {
          console.error('初始化失败:', error)
        } finally {
          set({ isLoading: false })
        }
      },
      
      // 重置状态
      reset: () => set({
        theme: 'system',
        user: null,
        isLoading: false,
      }),
    }),
    {
      name: 'app-storage',
      partialize: (state) => ({ 
        theme: state.theme,
        user: state.user 
      }),
    }
  )
)
```

### 功能模块 Store (`src/stores/feature-store.ts`)

```typescript
import { create } from "zustand"
import { subscribeWithSelector } from "zustand/middleware"

interface FeatureState {
  items: Item[]
  selectedId: string | null
  filter: FilterOptions
  
  // 计算属性
  get selectedItem(): Item | undefined
  get filteredItems(): Item[]
  
  // 操作方法
  addItem: (item: Item) => void
  removeItem: (id: string) => void
  updateItem: (id: string, updates: Partial<Item>) => void
  setSelectedId: (id: string | null) => void
  setFilter: (filter: FilterOptions) => void
}

export const useFeatureStore = create<FeatureState>()(
  subscribeWithSelector((set, get) => ({
    items: [],
    selectedId: null,
    filter: { status: 'all' },
    
    // 计算属性
    get selectedItem() {
      const { items, selectedId } = get()
      return items.find(item => item.id === selectedId)
    },
    
    get filteredItems() {
      const { items, filter } = get()
      if (filter.status === 'all') return items
      return items.filter(item => item.status === filter.status)
    },
    
    // 操作方法
    addItem: (item) => set((state) => ({
      items: [...state.items, item]
    })),
    
    removeItem: (id) => set((state) => ({
      items: state.items.filter(item => item.id !== id),
      selectedId: state.selectedId === id ? null : state.selectedId
    })),
    
    updateItem: (id, updates) => set((state) => ({
      items: state.items.map(item =>
        item.id === id ? { ...item, ...updates } : item
      )
    })),
    
    setSelectedId: (selectedId) => set({ selectedId }),
    
    setFilter: (filter) => set({ filter }),
  }))
)
```

## 高级用法

### 1. 订阅状态变化

```typescript
// 订阅特定状态
const unsubscribe = useFeatureStore.subscribe(
  (state) => state.selectedId,
  (selectedId) => {
    console.log('选中项变化:', selectedId)
  }
)

// 订阅多个状态
const unsubscribe = useFeatureStore.subscribe(
  (state) => ({ items: state.items, filter: state.filter }),
  ({ items, filter }) => {
    console.log('列表或过滤器变化')
  },
  { equalityFn: shallow }
)
```

### 2. 在组件外使用

```typescript
// 获取状态
const user = useAppStore.getState().user

// 更新状态
useAppStore.getState().setUser(newUser)

// 订阅变化
const unsubscribe = useAppStore.subscribe(
  (state) => state.user,
  (user) => {
    console.log('用户信息更新:', user)
  }
)
```

### 3. 组合多个 Store

```typescript
import { create } from "zustand"
import { combine } from "zustand/middleware"

// 使用 combine 中间件
export const useCombinedStore = create(
  combine(
    {
      // 初始状态
      count: 0,
      text: '',
    },
    (set) => ({
      // 操作方法
      increment: () => set((state) => ({ count: state.count + 1 })),
      decrement: () => set((state) => ({ count: state.count - 1 })),
      setText: (text: string) => set({ text }),
    })
  )
)
```

### 4. 异步操作模式

```typescript
interface AsyncState<T> {
  data: T | null
  isLoading: boolean
  error: Error | null
  
  fetch: () => Promise<void>
  reset: () => void
}

function createAsyncStore<T>(
  name: string,
  fetcher: () => Promise<T>
) {
  return create<AsyncState<T>>((set) => ({
    data: null,
    isLoading: false,
    error: null,
    
    fetch: async () => {
      set({ isLoading: true, error: null })
      try {
        const data = await fetcher()
        set({ data, isLoading: false })
      } catch (error) {
        set({ 
          error: error instanceof Error ? error : new Error('Unknown error'),
          isLoading: false 
        })
      }
    },
    
    reset: () => set({
      data: null,
      isLoading: false,
      error: null,
    }),
  }))
}

// 使用
export const useUserDataStore = createAsyncStore(
  'user-data',
  () => fetchUserData()
)
```

## 在组件中使用

### 基础使用

```tsx
import { useAppStore } from '@/stores/app-store'

export function UserProfile() {
  const user = useAppStore((state) => state.user)
  const setUser = useAppStore((state) => state.setUser)
  
  // 或者一次性获取多个值
  const { user, isLoading, setUser } = useAppStore((state) => ({
    user: state.user,
    isLoading: state.isLoading,
    setUser: state.setUser,
  }))
  
  return (
    <div>
      {isLoading ? (
        <p>加载中...</p>
      ) : user ? (
        <div>
          <h2>{user.name}</h2>
          <button onClick={() => setUser(null)}>退出登录</button>
        </div>
      ) : (
        <button onClick={() => setUser({ name: '测试用户' })}>
          登录
        </button>
      )}
    </div>
  )
}
```

### 性能优化

```tsx
import { shallow } from 'zustand/shallow'

export function ItemList() {
  // 使用 shallow 比较避免不必要的重渲染
  const { items, filter } = useFeatureStore(
    (state) => ({ items: state.items, filter: state.filter }),
    shallow
  )
  
  // 或者分别订阅
  const items = useFeatureStore((state) => state.items)
  const filter = useFeatureStore((state) => state.filter)
  
  return (
    <ul>
      {items.map(item => (
        <li key={item.id}>{item.name}</li>
      ))}
    </ul>
  )
}
```

## 最佳实践

### 1. Store 组织结构

```
src/stores/
├── app-store.ts        # 全局应用状态
├── auth-store.ts       # 认证相关状态
├── ui-store.ts         # UI 相关状态
└── [feature]/          # 功能模块状态
    └── feature-store.ts
```

### 2. 类型定义

```typescript
// 定义清晰的类型
interface User {
  id: string
  name: string
  email: string
  avatar?: string
}

// 使用类型别名简化
type Theme = 'light' | 'dark' | 'system'

// 导出类型供其他地方使用
export type { User, Theme }
```

### 3. 命名规范

- Store 文件：`[name]-store.ts`
- Hook 名称：`use[Name]Store`
- 操作方法：动词开头（set、update、remove、fetch 等）
- 状态属性：名词（user、items、isLoading 等）

### 4. 避免过度使用

- 不是所有状态都需要放在 Store 中
- 组件内部状态使用 useState
- 只有需要跨组件共享的状态才放入 Store

## 调试工具

### Redux DevTools 集成

```typescript
import { devtools } from 'zustand/middleware'

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set) => ({
        // ... store 定义
      }),
      { name: 'app-storage' }
    ),
    { name: 'AppStore' }
  )
)
```

### 日志中间件

```typescript
const log = (config) => (set, get, api) =>
  config(
    (...args) => {
      console.log('  applying', args)
      set(...args)
      console.log('  new state', get())
    },
    get,
    api
  )

export const useAppStore = create<AppState>()(
  log((set) => ({
    // ... store 定义
  }))
) 