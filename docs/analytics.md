# 📊 埋点分析系统

基于 PostHog 的用户行为分析和事件追踪系统。

## 核心功能

- **事件追踪** - 自动和手动事件追踪
- **用户识别** - 用户身份关联
- **页面浏览** - 自动页面浏览追踪
- **功能标记** - Feature Flags 支持
- **A/B 测试** - 实验功能支持

## 基础配置

### PostHog Provider (`src/providers/analytics-provider.tsx`)

```typescript
'use client'

import { createContext, useContext, useEffect, ReactNode } from 'react'
import posthog from 'posthog-js'
import { PostHogProvider as PHProvider } from 'posthog-js/react'

interface AnalyticsContextType {
  track: (event: string, properties?: Record<string, any>) => void
  identify: (userId: string, properties?: Record<string, any>) => void
  reset: () => void
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined)

export function useAnalytics() {
  const context = useContext(AnalyticsContext)
  if (!context) {
    throw new Error('useAnalytics must be used within AnalyticsProvider')
  }
  return context
}

interface AnalyticsProviderProps {
  children: ReactNode
}

export function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  useEffect(() => {
    if (process.env.NEXT_PUBLIC_POSTHOG_KEY) {
      posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
        api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
        capture_pageview: 'history_change',
        loaded: (posthog) => {
          if (process.env.NODE_ENV === 'development') {
            posthog.debug()
          }
        },
      })
    }
  }, [])

  const analytics: AnalyticsContextType = {
    track: (event, properties) => {
      posthog.capture(event, properties)
    },
    
    identify: (userId, properties) => {
      posthog.identify(userId, properties)
    },
    
    reset: () => {
      posthog.reset()
    },
  }

  return (
    <AnalyticsContext.Provider value={analytics}>
      <PHProvider client={posthog}>
        {children}
      </PHProvider>
    </AnalyticsContext.Provider>
  )
}
```

### 埋点 Hook (`src/hooks/use-tracking.ts`)

```typescript
import { useCallback } from 'react'
import { useAnalytics } from '@/providers/analytics-provider'

interface TrackingEvent {
  action: string
  category: string
  label?: string
  value?: number
  properties?: Record<string, any>
}

export function useTracking() {
  const { track } = useAnalytics()
  
  const trackEvent = useCallback((event: TrackingEvent) => {
    const { action, category, label, value, properties = {} } = event
    
    track(`${category}_${action}`, {
      category,
      action,
      label,
      value,
      timestamp: Date.now(),
      ...properties,
    })
  }, [track])
  
  const trackPageView = useCallback((page: string, properties?: Record<string, any>) => {
    track('page_view', {
      page,
      timestamp: Date.now(),
      ...properties,
    })
  }, [track])
  
  const trackUserAction = useCallback((action: string, properties?: Record<string, any>) => {
    track('user_action', {
      action,
      timestamp: Date.now(),
      ...properties,
    })
  }, [track])
  
  return {
    trackEvent,
    trackPageView,
    trackUserAction,
  }
}
```

## 使用指南

### 1. 在根布局中配置

```tsx
// src/app/layout.tsx
import { AnalyticsProvider } from '@/providers/analytics-provider'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body>
        <AnalyticsProvider>
          {children}
        </AnalyticsProvider>
      </body>
    </html>
  )
}
```

### 2. 环境变量配置

```bash
# .env.local
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

### 3. 在组件中使用

```tsx
import { useTracking } from '@/hooks/use-tracking'
import { useAnalytics } from '@/providers/analytics-provider'

export function ProductCard({ product }) {
  const { trackEvent } = useTracking()
  const { identify } = useAnalytics()
  
  const handleClick = () => {
    trackEvent({
      category: 'product',
      action: 'click',
      label: product.name,
      value: product.price,
      properties: {
        productId: product.id,
        productCategory: product.category,
      }
    })
  }
  
  const handlePurchase = () => {
    trackEvent({
      category: 'ecommerce',
      action: 'purchase',
      label: product.name,
      value: product.price,
      properties: {
        productId: product.id,
        quantity: 1,
        currency: 'CNY',
      }
    })
  }
  
  return (
    <div onClick={handleClick}>
      <h3>{product.name}</h3>
      <p>{product.price}</p>
      <button onClick={handlePurchase}>购买</button>
    </div>
  )
}
```

## 高级功能

### 1. 自动页面追踪

```tsx
// src/components/page-tracker.tsx
'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useTracking } from '@/hooks/use-tracking'

export function PageTracker() {
  const pathname = usePathname()
  const { trackPageView } = useTracking()
  
  useEffect(() => {
    trackPageView(pathname, {
      referrer: document.referrer,
      userAgent: navigator.userAgent,
    })
  }, [pathname, trackPageView])
  
  return null
}

// 在布局中使用
export default function Layout({ children }) {
  return (
    <>
      <PageTracker />
      {children}
    </>
  )
}
```

### 2. 表单追踪

```tsx
import { useTracking } from '@/hooks/use-tracking'

export function ContactForm() {
  const { trackEvent } = useTracking()
  
  const trackFormInteraction = (fieldName: string) => {
    trackEvent({
      category: 'form',
      action: 'field_focus',
      label: fieldName,
    })
  }
  
  const trackFormSubmit = (success: boolean) => {
    trackEvent({
      category: 'form',
      action: success ? 'submit_success' : 'submit_error',
      label: 'contact_form',
    })
  }
  
  const handleSubmit = async (data) => {
    try {
      await submitForm(data)
      trackFormSubmit(true)
    } catch (error) {
      trackFormSubmit(false)
    }
  }
  
  return (
    <form onSubmit={handleSubmit}>
      <input
        name="email"
        onFocus={() => trackFormInteraction('email')}
      />
      <textarea
        name="message"
        onFocus={() => trackFormInteraction('message')}
      />
      <button type="submit">提交</button>
    </form>
  )
}
```

### 3. 性能追踪

```tsx
// src/hooks/use-performance-tracking.ts
import { useEffect } from 'react'
import { useTracking } from './use-tracking'

export function usePerformanceTracking() {
  const { trackEvent } = useTracking()
  
  useEffect(() => {
    // 页面加载性能
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      if (navigation) {
        trackEvent({
          category: 'performance',
          action: 'page_load',
          properties: {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            domInteractive: navigation.domInteractive - navigation.fetchStart,
            firstPaint: navigation.responseEnd - navigation.fetchStart,
          }
        })
      }
    }
    
    // Core Web Vitals
    if ('web-vital' in window) {
      const reportWebVital = (metric: any) => {
        trackEvent({
          category: 'performance',
          action: 'web_vital',
          label: metric.name,
          value: Math.round(metric.value),
          properties: {
            metricName: metric.name,
            metricValue: metric.value,
            metricRating: metric.rating,
          }
        })
      }
      
      // 监听 Web Vitals
      // getCLS(reportWebVital)
      // getFID(reportWebVital)
      // getLCP(reportWebVital)
    }
  }, [trackEvent])
}
```

### 4. 错误追踪

```tsx
// src/hooks/use-error-tracking.ts
import { useEffect } from 'react'
import { useTracking } from './use-tracking'

export function useErrorTracking() {
  const { trackEvent } = useTracking()
  
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      trackEvent({
        category: 'error',
        action: 'javascript_error',
        label: event.message,
        properties: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
          userAgent: navigator.userAgent,
          url: window.location.href,
        }
      })
    }
    
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      trackEvent({
        category: 'error',
        action: 'unhandled_rejection',
        label: event.reason?.message || 'Unknown error',
        properties: {
          reason: event.reason,
          promise: event.promise,
          userAgent: navigator.userAgent,
          url: window.location.href,
        }
      })
    }
    
    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    
    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [trackEvent])
}
```

## 事件命名规范

### 1. 事件分类

- **page_view** - 页面浏览
- **user_action** - 用户操作
- **form_interaction** - 表单交互
- **ecommerce** - 电商相关
- **performance** - 性能指标
- **error** - 错误事件

### 2. 命名格式

```
{category}_{action}

例如：
- product_click
- form_submit
- ecommerce_purchase
- error_javascript_error
```

### 3. 属性规范

```typescript
interface StandardProperties {
  timestamp: number      // 时间戳
  userId?: string       // 用户ID
  sessionId?: string    // 会话ID
  page?: string         // 当前页面
  referrer?: string     // 来源页面
  userAgent?: string    // 用户代理
}
```

## 数据分析示例

### 1. 用户行为漏斗

```typescript
// 追踪用户转化漏斗
const trackFunnelStep = (step: string, properties?: any) => {
  trackEvent({
    category: 'funnel',
    action: 'step_completed',
    label: step,
    properties: {
      funnelName: 'user_onboarding',
      step,
      ...properties,
    }
  })
}

// 使用
trackFunnelStep('registration_started')
trackFunnelStep('email_verified')
trackFunnelStep('profile_completed')
trackFunnelStep('onboarding_finished')
```

### 2. A/B 测试追踪

```typescript
import { usePostHog } from 'posthog-js/react'

export function ABTestComponent() {
  const posthog = usePostHog()
  const variant = posthog?.getFeatureFlag('new-feature-test')
  
  useEffect(() => {
    if (variant) {
      trackEvent({
        category: 'ab_test',
        action: 'variant_shown',
        label: 'new-feature-test',
        properties: {
          variant,
          testName: 'new-feature-test',
        }
      })
    }
  }, [variant])
  
  return variant === 'control' ? <OldComponent /> : <NewComponent />
}
```

## 最佳实践

1. **隐私保护** - 不要追踪敏感信息（密码、信用卡号等）
2. **数据最小化** - 只收集必要的数据
3. **用户同意** - 遵守 GDPR 等隐私法规
4. **性能考虑** - 避免过度追踪影响性能
5. **数据准确性** - 确保事件名称和属性的一致性
6. **文档维护** - 维护事件追踪文档
7. **定期审查** - 定期审查和清理无用的追踪事件 