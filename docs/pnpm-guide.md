# 📦 pnpm 使用指南

使用 pnpm 进行高效的依赖管理。

## 什么是 pnpm

pnpm 是一个快速、节省磁盘空间的包管理器。它的主要特点：

- **节省磁盘空间** - 使用硬链接和符号链接避免重复安装
- **更快的安装速度** - 并行安装和智能缓存
- **严格的依赖管理** - 避免幽灵依赖问题
- **支持 Monorepo** - 内置 workspace 支持

## 安装 pnpm

### 使用 npm 安装

```bash
npm install -g pnpm
```

### 使用 Homebrew (macOS)

```bash
brew install pnpm
```

### 使用脚本安装

```bash
curl -fsSL https://get.pnpm.io/install.sh | sh -
```

### 验证安装

```bash
pnpm --version
```

## 基础命令

### 项目初始化

```bash
# 创建 package.json
pnpm init

# 从现有项目迁移
pnpm import  # 从其他包管理器的 lock 文件导入
```

### 依赖管理

```bash
# 安装所有依赖
pnpm install
pnpm i  # 简写

# 添加依赖
pnpm add <package>
pnpm add react react-dom

# 添加开发依赖
pnpm add -D <package>
pnpm add -D @types/react

# 添加全局依赖
pnpm add -g <package>

# 移除依赖
pnpm remove <package>
pnpm rm <package>  # 简写

# 更新依赖
pnpm update
pnpm up  # 简写

# 更新特定包
pnpm update <package>

# 查看过期的包
pnpm outdated
```

### 运行脚本

```bash
# 运行 package.json 中的脚本
pnpm run <script>
pnpm <script>  # 如果没有与 pnpm 命令冲突

# 示例
pnpm dev
pnpm build
pnpm test
```

### 查看依赖

```bash
# 查看依赖树
pnpm list
pnpm ls  # 简写

# 查看顶层依赖
pnpm list --depth=0

# 查看特定包的依赖
pnpm list <package>

# 查看为什么安装了某个包
pnpm why <package>
```

## 配置文件

### .npmrc 配置

创建 `.npmrc` 文件进行项目级配置：

```ini
# pnpm 配置
strict-peer-dependencies=true
auto-install-peers=true
shamefully-hoist=true

# 存储配置
store-dir=~/.pnpm-store

# 网络配置
network-timeout=60000

# 注册表配置
registry=https://registry.npmjs.org/

# 如果使用私有注册表
# @your-scope:registry=https://your-private-registry.com/

# 如果需要代理
# proxy=http://proxy.company.com:8080/
# https-proxy=http://proxy.company.com:8080/
```

### package.json 配置

指定 pnpm 版本：

```json
{
  "packageManager": "pnpm@9.0.0",
  "engines": {
    "node": ">=18.0.0",
    "pnpm": ">=9.0.0"
  }
}
```

## Workspace (Monorepo) 支持

### pnpm-workspace.yaml

```yaml
packages:
  # 所有在 packages/ 目录下的包
  - 'packages/*'
  # 所有在 apps/ 目录下的应用
  - 'apps/*'
  # 排除测试目录
  - '!**/test/**'
```

### Workspace 命令

```bash
# 在根目录安装所有依赖
pnpm install

# 在特定 workspace 运行命令
pnpm --filter <package-name> <command>

# 示例：在 web 应用中添加依赖
pnpm --filter web add react

# 在所有 workspace 运行命令
pnpm -r <command>

# 示例：在所有包中运行 build
pnpm -r build

# 并行运行命令
pnpm -r --parallel build
```

## 性能优化

### 1. 使用缓存

```bash
# 查看缓存信息
pnpm store status

# 清理未使用的包
pnpm store prune

# 验证存储完整性
pnpm store verify
```

### 2. 配置并发

```ini
# .npmrc
network-concurrency=16
fetch-retries=3
fetch-retry-factor=2
fetch-retry-mintimeout=10000
fetch-retry-maxtimeout=60000
```

### 3. 使用离线模式

```bash
# 优先使用缓存
pnpm install --prefer-offline

# 完全离线模式
pnpm install --offline
```

## 常见问题解决

### 1. 幽灵依赖问题

pnpm 默认使用严格的依赖解析，如果遇到找不到模块的错误：

```ini
# .npmrc
# 临时解决方案（不推荐长期使用）
shamefully-hoist=true

# 或者只提升特定包
public-hoist-pattern[]=*eslint*
public-hoist-pattern[]=*prettier*
```

### 2. Peer Dependencies 警告

```ini
# .npmrc
# 自动安装 peer dependencies
auto-install-peers=true

# 或忽略 peer dependencies 警告
strict-peer-dependencies=false
```

### 3. 权限问题

```bash
# 修复权限问题
pnpm install --no-optional

# 使用不同的存储位置
pnpm config set store-dir /path/to/.pnpm-store
```

## 迁移指南

### 从 npm/yarn 迁移

1. **删除旧的 lock 文件和 node_modules**
   ```bash
   rm -rf node_modules package-lock.json yarn.lock
   ```

2. **安装依赖**
   ```bash
   pnpm install
   ```

3. **更新脚本**
   ```json
   {
     "scripts": {
       "preinstall": "npx only-allow pnpm"
     }
   }
   ```

### 导入现有 lock 文件

```bash
# 从 package-lock.json 导入
pnpm import

# 生成 pnpm-lock.yaml
pnpm install
```

## 高级用法

### 1. 补丁包

修复第三方包的 bug：

```bash
# 创建补丁
pnpm patch <package>

# 应用补丁
pnpm patch-commit <path-to-package>
```

### 2. 覆盖依赖版本

在 `package.json` 中：

```json
{
  "pnpm": {
    "overrides": {
      "lodash": "^4.17.21",
      "react": "^18.0.0"
    }
  }
}
```

### 3. 钩子脚本

在 `.pnpmfile.cjs` 中：

```javascript
module.exports = {
  hooks: {
    readPackage(pkg) {
      // 修改包的依赖
      if (pkg.name === 'example-package') {
        pkg.dependencies['lodash'] = '^4.17.21'
      }
      return pkg
    }
  }
}
```

## 最佳实践

### 1. 版本锁定

- 始终提交 `pnpm-lock.yaml` 文件
- 在 CI/CD 中使用 `pnpm install --frozen-lockfile`

### 2. 脚本优化

```json
{
  "scripts": {
    "preinstall": "npx only-allow pnpm",
    "prepare": "husky install",
    "clean": "pnpm -r exec rm -rf node_modules dist",
    "fresh": "pnpm clean && pnpm install"
  }
}
```

### 3. CI/CD 配置

```yaml
# GitHub Actions 示例
- name: Setup pnpm
  uses: pnpm/action-setup@v2
  with:
    version: 9

- name: Install dependencies
  run: pnpm install --frozen-lockfile

- name: Build
  run: pnpm build
```

### 4. 安全性

```bash
# 审计依赖
pnpm audit

# 修复安全问题
pnpm audit --fix

# 查看许可证
pnpm licenses list
```

## 常用命令速查

| 命令 | 说明 |
|------|------|
| `pnpm install` | 安装所有依赖 |
| `pnpm add <pkg>` | 添加依赖 |
| `pnpm add -D <pkg>` | 添加开发依赖 |
| `pnpm remove <pkg>` | 移除依赖 |
| `pnpm update` | 更新依赖 |
| `pnpm list` | 查看依赖树 |
| `pnpm outdated` | 查看过期包 |
| `pnpm why <pkg>` | 查看为什么安装 |
| `pnpm store prune` | 清理存储 |
| `pnpm -r build` | workspace 构建 |

## 故障排除

### 清理和重置

```bash
# 清理缓存
pnpm store prune

# 删除 node_modules 和重新安装
rm -rf node_modules
pnpm install

# 完全重置
rm -rf node_modules pnpm-lock.yaml
rm -rf ~/.pnpm-store
pnpm install
```

### 调试模式

```bash
# 启用调试日志
DEBUG=pnpm* pnpm install

# 查看详细日志
pnpm install --reporter=append-only
``` 