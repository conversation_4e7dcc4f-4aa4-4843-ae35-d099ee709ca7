# 🪝 通用 Hooks

常用的自定义 React Hooks 集合，提高开发效率和代码复用。

## 核心 Hooks

### 1. 防抖 Hook (`src/hooks/use-debounce.ts`)

用于延迟执行函数或值的更新，避免频繁触发。

```typescript
import { useState, useEffect } from 'react'

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout>()

  const debouncedCallback = ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    const newTimeoutId = setTimeout(() => {
      callback(...args)
    }, delay)

    setTimeoutId(newTimeoutId)
  }) as T

  return debouncedCallback
}
```

#### 使用示例

```tsx
// 防抖搜索输入
export function SearchInput() {
  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, 500)
  
  useEffect(() => {
    if (debouncedSearchTerm) {
      // 执行搜索
      performSearch(debouncedSearchTerm)
    }
  }, [debouncedSearchTerm])
  
  return (
    <input
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      placeholder="搜索..."
    />
  )
}

// 防抖回调函数
export function AutoSaveForm() {
  const saveForm = useDebouncedCallback((data) => {
    console.log('保存表单:', data)
  }, 1000)
  
  return (
    <form onChange={(e) => saveForm(new FormData(e.currentTarget))}>
      {/* 表单内容 */}
    </form>
  )
}
```

### 2. 节流 Hook (`src/hooks/use-throttle.ts`)

限制函数在指定时间内的执行频率。

```typescript
import { useRef, useCallback } from 'react'

export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now())

  return useCallback(
    ((...args: Parameters<T>) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args)
        lastRun.current = Date.now()
      }
    }) as T,
    [callback, delay]
  )
}
```

### 3. 本地存储 Hook (`src/hooks/use-local-storage.ts`)

同步状态与 localStorage 的 Hook。

```typescript
import { useState, useEffect } from 'react'

export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  // 获取初始值
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue
    }
    
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.error(`Error loading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  // 更新值的函数
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }

  return [storedValue, setValue]
}
```

### 4. 媒体查询 Hook (`src/hooks/use-media-query.ts`)

响应式设计的媒体查询 Hook。

```typescript
import { useState, useEffect } from 'react'

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    const media = window.matchMedia(query)
    if (media.matches !== matches) {
      setMatches(media.matches)
    }

    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    media.addEventListener('change', listener)
    return () => media.removeEventListener('change', listener)
  }, [matches, query])

  return matches
}

// 预定义的断点
export function useBreakpoint() {
  const isMobile = useMediaQuery('(max-width: 640px)')
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)')
  const isDesktop = useMediaQuery('(min-width: 1025px)')
  
  return { isMobile, isTablet, isDesktop }
}
```

### 5. 异步状态 Hook (`src/hooks/use-async.ts`)

管理异步操作状态的 Hook。

```typescript
import { useState, useCallback } from 'react'

interface AsyncState<T> {
  data: T | null
  error: Error | null
  loading: boolean
}

export function useAsync<T>() {
  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    error: null,
    loading: false,
  })

  const execute = useCallback(async (asyncFunction: () => Promise<T>) => {
    setState({ data: null, error: null, loading: true })
    
    try {
      const data = await asyncFunction()
      setState({ data, error: null, loading: false })
      return data
    } catch (error) {
      setState({ 
        data: null, 
        error: error instanceof Error ? error : new Error('Unknown error'), 
        loading: false 
      })
      throw error
    }
  }, [])

  return { ...state, execute }
}
```

### 6. 倒计时 Hook (`src/hooks/use-countdown.ts`)

实现倒计时功能的 Hook。

```typescript
import { useState, useEffect, useRef } from 'react'

interface CountdownOptions {
  onComplete?: () => void
  interval?: number
}

export function useCountdown(
  initialTime: number,
  options: CountdownOptions = {}
) {
  const { onComplete, interval = 1000 } = options
  const [timeLeft, setTimeLeft] = useState(initialTime)
  const [isActive, setIsActive] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (isActive && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft((time) => {
          if (time <= 1) {
            setIsActive(false)
            onComplete?.()
            return 0
          }
          return time - 1
        })
      }, interval)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isActive, timeLeft, interval, onComplete])

  const start = () => setIsActive(true)
  const pause = () => setIsActive(false)
  const reset = () => {
    setIsActive(false)
    setTimeLeft(initialTime)
  }

  return { timeLeft, isActive, start, pause, reset }
}
```

### 7. 点击外部 Hook (`src/hooks/use-click-outside.ts`)

检测点击元素外部的 Hook。

```typescript
import { useEffect, RefObject } from 'react'

export function useClickOutside<T extends HTMLElement = HTMLElement>(
  ref: RefObject<T>,
  handler: (event: MouseEvent | TouchEvent) => void
) {
  useEffect(() => {
    const listener = (event: MouseEvent | TouchEvent) => {
      const el = ref?.current
      if (!el || el.contains((event?.target as Node) || null)) {
        return
      }
      handler(event)
    }

    document.addEventListener('mousedown', listener)
    document.addEventListener('touchstart', listener)

    return () => {
      document.removeEventListener('mousedown', listener)
      document.removeEventListener('touchstart', listener)
    }
  }, [ref, handler])
}
```

### 8. 滚动位置 Hook (`src/hooks/use-scroll.ts`)

监听和控制滚动位置的 Hook。

```typescript
import { useState, useEffect } from 'react'

interface ScrollPosition {
  x: number
  y: number
}

export function useScroll() {
  const [scrollPosition, setScrollPosition] = useState<ScrollPosition>({
    x: 0,
    y: 0,
  })

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition({
        x: window.scrollX,
        y: window.scrollY,
      })
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    handleScroll()

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const scrollTo = (x: number, y: number, smooth = true) => {
    window.scrollTo({
      left: x,
      top: y,
      behavior: smooth ? 'smooth' : 'auto',
    })
  }

  const scrollToTop = (smooth = true) => scrollTo(0, 0, smooth)
  const scrollToBottom = (smooth = true) => {
    scrollTo(0, document.documentElement.scrollHeight, smooth)
  }

  return {
    ...scrollPosition,
    scrollTo,
    scrollToTop,
    scrollToBottom,
  }
}
```

### 9. 复制到剪贴板 Hook (`src/hooks/use-clipboard.ts`)

处理剪贴板操作的 Hook。

```typescript
import { useState } from 'react'

export function useClipboard(timeout = 2000) {
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const copy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setError(null)
      
      setTimeout(() => setCopied(false), timeout)
    } catch (err) {
      setCopied(false)
      setError(err instanceof Error ? err : new Error('Failed to copy'))
    }
  }

  return { copied, error, copy }
}
```

### 10. 键盘快捷键 Hook (`src/hooks/use-hotkeys.ts`)

处理键盘快捷键的 Hook。

```typescript
import { useEffect, useRef } from 'react'

type KeyHandler = (event: KeyboardEvent) => void

interface HotkeyOptions {
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  meta?: boolean
  preventDefault?: boolean
}

export function useHotkey(
  key: string,
  handler: KeyHandler,
  options: HotkeyOptions = {}
) {
  const { ctrl, shift, alt, meta, preventDefault = true } = options
  const handlerRef = useRef(handler)
  
  useEffect(() => {
    handlerRef.current = handler
  }, [handler])

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.key.toLowerCase() === key.toLowerCase() &&
        (!ctrl || event.ctrlKey) &&
        (!shift || event.shiftKey) &&
        (!alt || event.altKey) &&
        (!meta || event.metaKey)
      ) {
        if (preventDefault) {
          event.preventDefault()
        }
        handlerRef.current(event)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [key, ctrl, shift, alt, meta, preventDefault])
}
```

## 使用示例

### 组合多个 Hooks

```tsx
export function AdvancedSearchComponent() {
  const [query, setQuery] = useState('')
  const debouncedQuery = useDebounce(query, 300)
  const { data, loading, error, execute } = useAsync<SearchResults>()
  const { isMobile } = useBreakpoint()
  
  useEffect(() => {
    if (debouncedQuery) {
      execute(() => searchAPI(debouncedQuery))
    }
  }, [debouncedQuery, execute])
  
  useHotkey('/', () => {
    document.getElementById('search-input')?.focus()
  }, { preventDefault: true })
  
  return (
    <div className={isMobile ? 'mobile-search' : 'desktop-search'}>
      <input
        id="search-input"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="按 / 键聚焦搜索"
      />
      
      {loading && <div>搜索中...</div>}
      {error && <div>搜索出错: {error.message}</div>}
      {data && <SearchResults results={data} />}
    </div>
  )
}
```

## 最佳实践

1. **命名规范** - Hook 名称以 `use` 开头
2. **纯函数** - Hook 应该是纯函数，避免副作用
3. **依赖管理** - 正确声明 useEffect 的依赖项
4. **错误处理** - 提供完善的错误处理机制
5. **类型安全** - 使用 TypeScript 提供类型定义
6. **性能优化** - 使用 useMemo 和 useCallback 优化性能
7. **文档完善** - 为每个 Hook 提供使用示例和说明 