# 🌉 Flutter Hybrid 容器系统

基于 flutter_inappwebview 实现的 H5 容器，提供与 Flutter 的双向通信能力。

## 核心功能

- **双向通信** - Web ↔ Flutter 消息传递
- **状态监控** - 实时检测 Flutter 连接状态
- **消息队列** - 管理接收的消息
- **类型安全** - TypeScript 类型定义
- **错误处理** - 完善的错误处理机制

## 基础实现

### 桥接 Provider (`src/providers/flutter-bridge-provider.tsx`)

```typescript
'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// 声明全局的flutter_inappwebview对象
declare global {
  interface Window {
    flutter_inappwebview?: {
      callHandler: (handlerName: string, ...args: any[]) => void
    }
    receiveMessageFromFlutter?: (message: string) => void
  }
}

interface FlutterBridgeContextType {
  isFlutterAvailable: boolean
  receivedMessages: string[]
  sendMessageToFlutter: (message: string) => void
  clearReceivedMessages: () => void
}

const FlutterBridgeContext = createContext<FlutterBridgeContextType | undefined>(undefined)

export function useFlutterBridge() {
  const context = useContext(FlutterBridgeContext)
  if (context === undefined) {
    throw new Error('useFlutterBridge must be used within a FlutterBridgeProvider')
  }
  return context
}

interface FlutterBridgeProviderProps {
  children: ReactNode
}

export function FlutterBridgeProvider({ children }: FlutterBridgeProviderProps) {
  const [isFlutterAvailable, setIsFlutterAvailable] = useState(false)
  const [receivedMessages, setReceivedMessages] = useState<string[]>([])
  
  useEffect(() => {
    // 检查Flutter WebView是否可用
    const checkFlutterAvailability = () => {
      if (window.flutter_inappwebview) {
        setIsFlutterAvailable(true)
        console.log('Flutter WebView bridge is available')
      } else {
        setIsFlutterAvailable(false)
        console.log('Flutter WebView bridge is not available')
      }
    }

    // 定义接收Flutter消息的全局函数
    window.receiveMessageFromFlutter = (message: string) => {
      console.log('Received message from Flutter:', message)
      setReceivedMessages(prev => [...prev, message])
    }

    // 初始检查
    checkFlutterAvailability()

    // 定期检查Flutter可用性
    const interval = setInterval(checkFlutterAvailability, 1000)

    return () => {
      clearInterval(interval)
      // 清理全局函数
      if (window.receiveMessageFromFlutter) {
        window.receiveMessageFromFlutter = undefined
      }
    }
  }, [])

  const sendMessageToFlutter = (message: string) => {
    if (!message.trim()) {
      throw new Error('消息内容不能为空')
    }

    if (!window.flutter_inappwebview) {
      throw new Error('Flutter WebView bridge 不可用')
    }

    try {
      // 发送消息到Flutter
      window.flutter_inappwebview.callHandler('messageHandler', message)
      console.log('Sent message to Flutter:', message)
    } catch (error) {
      console.error('Error sending message to Flutter:', error)
      throw error
    }
  }

  const clearReceivedMessages = () => {
    setReceivedMessages([])
  }

  const value = {
    isFlutterAvailable,
    receivedMessages,
    sendMessageToFlutter,
    clearReceivedMessages,
  }

  return (
    <FlutterBridgeContext.Provider value={value}>
      {children}
    </FlutterBridgeContext.Provider>
  )
}
```

### Flutter 桥接示例组件 (`src/components/flutter-bridge-example.tsx`)

```typescript
'use client'

import { useState } from 'react'
import { useFlutterBridge } from '@/providers/flutter-bridge-provider'

export function FlutterBridgeExample() {
  const [message, setMessage] = useState('')
  const { isFlutterAvailable, receivedMessages, sendMessageToFlutter } = useFlutterBridge()

  const handleSendMessage = () => {
    if (message.trim()) {
      try {
        sendMessageToFlutter(message)
        setMessage('')
      } catch (error) {
        console.error('发送消息失败:', error)
      }
    }
  }

  return (
    <div className="p-4 space-y-4">
      <div className="bg-card p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Flutter 桥接状态</h3>
        <p className={`text-sm ${isFlutterAvailable ? 'text-green-600' : 'text-red-600'}`}>
          {isFlutterAvailable ? '✅ Flutter 桥接可用' : '❌ Flutter 桥接不可用'}
        </p>
      </div>

      <div className="bg-card p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">发送消息到 Flutter</h3>
        <div className="flex gap-2">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="输入消息..."
            className="flex-1 px-3 py-2 border rounded-md"
            disabled={!isFlutterAvailable}
          />
          <button
            onClick={handleSendMessage}
            disabled={!isFlutterAvailable || !message.trim()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md disabled:opacity-50"
          >
            发送
          </button>
        </div>
      </div>

      <div className="bg-card p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">接收的消息</h3>
        {receivedMessages.length > 0 ? (
          <ul className="space-y-1">
            {receivedMessages.map((msg, index) => (
              <li key={index} className="text-sm p-2 bg-muted rounded">
                {msg}
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-muted-foreground">暂无消息</p>
        )}
      </div>
    </div>
  )
}
```

## 使用指南

### 1. 集成步骤

#### 在根布局中配置

```tsx
// src/app/layout.tsx
import { FlutterBridgeProvider } from "@/providers/flutter-bridge-provider"

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body>
        <FlutterBridgeProvider>
          {children}
        </FlutterBridgeProvider>
      </body>
    </html>
  )
}
```

#### 在页面中使用

```tsx
'use client'

import { useEffect } from 'react'
import { useFlutterBridge } from '@/providers/flutter-bridge-provider'
import { FlutterBridgeExample } from '@/components/flutter-bridge-example'

export default function HomePage() {
  const { isFlutterAvailable, sendMessageToFlutter } = useFlutterBridge()
  
  useEffect(() => {
    // 页面加载完成后通知 Flutter
    if (isFlutterAvailable) {
      try {
        sendMessageToFlutter(JSON.stringify({ 
          type: 'page_ready',
          page: 'home' 
        }))
      } catch (error) {
        console.error('通知 Flutter 失败:', error)
      }
    }
  }, [isFlutterAvailable, sendMessageToFlutter])
  
  // 处理特定业务逻辑
  const handleOnboardingComplete = () => {
    if (isFlutterAvailable) {
      sendMessageToFlutter(JSON.stringify({ 
        type: 'onboarding_completed',
        success: true 
      }))
    }
  }
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Flutter WebView 集成示例</h1>
      
      <FlutterBridgeExample />
      
      <div className="mt-8">
        <button
          onClick={handleOnboardingComplete}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
          disabled={!isFlutterAvailable}
        >
          模拟 Onboarding 完成
        </button>
      </div>
    </div>
  )
}
```

### 2. 消息格式规范

建议使用 JSON 格式传递结构化数据：

```typescript
// 定义消息类型
interface FlutterMessage {
  type: string
  payload?: any
  timestamp?: number
}

// 发送消息
const sendStructuredMessage = (type: string, payload?: any) => {
  const message: FlutterMessage = {
    type,
    payload,
    timestamp: Date.now()
  }
  sendMessageToFlutter(JSON.stringify(message))
}

// 解析接收的消息
const parseReceivedMessage = (message: string): FlutterMessage | null => {
  try {
    return JSON.parse(message)
  } catch (error) {
    console.error('解析消息失败:', error)
    return null
  }
}
```

### 3. 常见消息类型

```typescript
// 页面状态消息
sendStructuredMessage('page_ready', { page: 'home' })
sendStructuredMessage('page_error', { error: 'Network error' })

// 用户操作消息
sendStructuredMessage('user_login', { userId: '123' })
sendStructuredMessage('user_logout')

// 业务流程消息
sendStructuredMessage('onboarding_start')
sendStructuredMessage('onboarding_complete', { success: true })

// 数据同步消息
sendStructuredMessage('data_update', { 
  type: 'user_profile',
  data: { name: 'John', email: '<EMAIL>' }
})
```

## 高级功能

### 1. 消息队列管理

```typescript
import { create } from 'zustand'

interface MessageQueueState {
  queue: Array<{ id: string; message: string; timestamp: number }>
  addMessage: (message: string) => void
  removeMessage: (id: string) => void
  clearQueue: () => void
}

export const useMessageQueue = create<MessageQueueState>((set) => ({
  queue: [],
  
  addMessage: (message) => set((state) => ({
    queue: [...state.queue, {
      id: Math.random().toString(36).substr(2, 9),
      message,
      timestamp: Date.now()
    }]
  })),
  
  removeMessage: (id) => set((state) => ({
    queue: state.queue.filter(msg => msg.id !== id)
  })),
  
  clearQueue: () => set({ queue: [] })
}))
```

### 2. 重试机制

```typescript
const sendMessageWithRetry = async (
  message: string,
  maxRetries = 3,
  retryDelay = 1000
) => {
  let retries = 0
  
  while (retries < maxRetries) {
    try {
      sendMessageToFlutter(message)
      return // 成功发送
    } catch (error) {
      retries++
      if (retries >= maxRetries) {
        throw new Error(`发送失败，已重试 ${maxRetries} 次`)
      }
      await new Promise(resolve => setTimeout(resolve, retryDelay))
    }
  }
}
```

### 3. 批量消息处理

```typescript
const sendBatchMessages = async (messages: string[]) => {
  const results = await Promise.allSettled(
    messages.map(msg => sendMessageToFlutter(msg))
  )
  
  const failed = results.filter(r => r.status === 'rejected')
  if (failed.length > 0) {
    console.error(`${failed.length} 条消息发送失败`)
  }
  
  return results
}
```

## 调试技巧

### 1. 开发环境模拟

```typescript
// 在开发环境模拟 Flutter 接口
if (process.env.NODE_ENV === 'development') {
  window.flutter_inappwebview = {
    callHandler: (name, data) => {
      console.log('Mock Flutter call:', name, data)
      
      // 模拟异步响应
      setTimeout(() => {
        if (window.receiveMessageFromFlutter) {
          window.receiveMessageFromFlutter(
            JSON.stringify({
              type: 'mock_response',
              originalMessage: data
            })
          )
        }
      }, 100)
    }
  }
}
```

### 2. 消息日志记录

```typescript
// 创建消息日志 Hook
export function useMessageLogger() {
  const { sendMessageToFlutter: originalSend } = useFlutterBridge()
  
  const sendMessageToFlutter = (message: string) => {
    console.group('📤 Sending to Flutter')
    console.log('Time:', new Date().toISOString())
    console.log('Message:', message)
    console.groupEnd()
    
    return originalSend(message)
  }
  
  return { sendMessageToFlutter }
}
```

### 3. 性能监控

```typescript
const measureMessagePerformance = async (message: string) => {
  const startTime = performance.now()
  
  try {
    await sendMessageToFlutter(message)
    const endTime = performance.now()
    const duration = endTime - startTime
    
    console.log(`消息发送耗时: ${duration.toFixed(2)}ms`)
    
    // 记录到分析系统
    trackEvent({
      category: 'flutter_bridge',
      action: 'message_sent',
      value: Math.round(duration),
      properties: {
        messageLength: message.length,
        duration
      }
    })
  } catch (error) {
    console.error('消息发送失败:', error)
  }
}
```

## 注意事项

### 连接状态监控
- 使用 `isFlutterAvailable` 检查桥接是否可用
- 定期（每秒）自动检查连接状态
- 在发送消息前始终检查连接状态

### 消息格式
- 发送的消息必须是字符串格式
- 建议使用 JSON.stringify() 序列化复杂数据
- 接收的消息会自动添加到 `receivedMessages` 数组

### 错误处理
- 发送空消息会抛出错误
- Flutter 不可用时发送消息会抛出错误
- 使用 try-catch 包装发送操作以处理异常

### 性能优化
- 避免频繁发送大量消息
- 及时清理不需要的接收消息
- 考虑使用消息批处理
- 监控消息发送和接收的性能

## Flutter 端集成示例

```dart
// Flutter 端代码示例
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebViewScreen extends StatefulWidget {
  @override
  _WebViewScreenState createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  InAppWebViewController? webViewController;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: InAppWebView(
        initialUrlRequest: URLRequest(
          url: Uri.parse('https://your-web-app.com'),
        ),
        onWebViewCreated: (controller) {
          webViewController = controller;
          
          // 注册消息处理器
          controller.addJavaScriptHandler(
            handlerName: 'messageHandler',
            callback: (args) {
              print('Received from Web: ${args[0]}');
              
              // 处理接收到的消息
              handleWebMessage(args[0]);
            },
          );
        },
      ),
    );
  }
  
  // 发送消息到 Web
  void sendMessageToWeb(String message) {
    webViewController?.evaluateJavascript(
      source: 'window.receiveMessageFromFlutter("$message")',
    );
  }
  
  // 处理来自 Web 的消息
  void handleWebMessage(String message) {
    // 解析和处理消息
    try {
      final data = json.decode(message);
      switch (data['type']) {
        case 'page_ready':
          print('Web page is ready');
          break;
        case 'onboarding_completed':
          print('Onboarding completed');
          break;
        default:
          print('Unknown message type: ${data['type']}');
      }
    } catch (e) {
      print('Error parsing message: $e');
    }
  }
}
``` 