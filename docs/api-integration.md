# 🌐 API 请求封装

统一的网络请求处理和错误管理系统。

## 核心功能

- **统一请求封装** - 基于 Fetch API 的统一请求处理
- **自动重试机制** - 网络错误和服务器错误自动重试
- **超时控制** - 可配置的请求超时
- **拦截器支持** - 请求和响应拦截器
- **类型安全** - 完整的 TypeScript 类型支持

## 基础实现

### 网络请求工具 (`src/lib/api/fetch-wrapper.ts`)

```typescript
// 请求配置接口
interface RequestConfig extends RequestInit {
  timeout?: number
  retries?: number
  retryDelay?: number
}

// 响应类型
interface ApiResponse<T = any> {
  data: T
  message: string
  code: number
  success: boolean
}

// 错误类型
class ApiError extends Error {
  constructor(
    public code: number,
    public message: string,
    public data?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// 获取认证令牌的辅助函数
function getAuthToken(): string | null {
  // 从localStorage、cookie或其他地方获取token
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth_token')
  }
  return null
}

// 延迟函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 判断是否应该重试
function shouldRetry(error: any): boolean {
  // 网络错误或5xx服务器错误可以重试
  return error.name === 'AbortError' || 
         (error.code >= 500 && error.code < 600) ||
         error.name === 'TypeError' // 网络错误
}

// 请求拦截器
const requestInterceptor = (config: RequestConfig): RequestConfig => {
  // 添加认证头
  const token = getAuthToken()
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`,
    }
  }
  
  // 添加通用头部
  config.headers = {
    'Content-Type': 'application/json',
    ...config.headers,
  }
  
  return config
}

// 响应拦截器
const responseInterceptor = async (response: Response): Promise<any> => {
  if (!response.ok) {
    const error = await response.json().catch(() => ({}))
    throw new ApiError(
      response.status,
      error.message || response.statusText,
      error
    )
  }
  
  return response.json()
}

// 主要请求函数
export async function fetchAPI<T = any>(
  url: string,
  config: RequestConfig = {}
): Promise<ApiResponse<T>> {
  const {
    timeout = 10000,
    retries = 3,
    retryDelay = 1000,
    ...requestConfig
  } = requestInterceptor(config)
  
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)
  
  try {
    const response = await fetch(url, {
      ...requestConfig,
      signal: controller.signal,
    })
    
    clearTimeout(timeoutId)
    return await responseInterceptor(response)
  } catch (error) {
    clearTimeout(timeoutId)
    
    // 重试逻辑
    if (retries > 0 && shouldRetry(error)) {
      await delay(retryDelay)
      return fetchAPI(url, { ...config, retries: retries - 1 })
    }
    
    throw error
  }
}

// 便捷方法
export const api = {
  get: <T>(url: string, config?: RequestConfig) => 
    fetchAPI<T>(url, { ...config, method: 'GET' }),
  
  post: <T>(url: string, data?: any, config?: RequestConfig) =>
    fetchAPI<T>(url, {
      ...config,
      method: 'POST',
      body: JSON.stringify(data),
    }),
  
  put: <T>(url: string, data?: any, config?: RequestConfig) =>
    fetchAPI<T>(url, {
      ...config,
      method: 'PUT',
      body: JSON.stringify(data),
    }),
  
  delete: <T>(url: string, config?: RequestConfig) =>
    fetchAPI<T>(url, { ...config, method: 'DELETE' }),
}
```

### API 端点定义 (`src/lib/api/endpoints.ts`)

```typescript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api'

export const endpoints = {
  // 认证相关
  auth: {
    login: `${API_BASE_URL}/auth/login`,
    logout: `${API_BASE_URL}/auth/logout`,
    refresh: `${API_BASE_URL}/auth/refresh`,
    profile: `${API_BASE_URL}/auth/profile`,
  },
  
  // 用户相关
  users: {
    list: `${API_BASE_URL}/users`,
    detail: (id: string) => `${API_BASE_URL}/users/${id}`,
    create: `${API_BASE_URL}/users`,
    update: (id: string) => `${API_BASE_URL}/users/${id}`,
    delete: (id: string) => `${API_BASE_URL}/users/${id}`,
  },
  
  // 其他功能模块
  posts: {
    list: `${API_BASE_URL}/posts`,
    detail: (id: string) => `${API_BASE_URL}/posts/${id}`,
    create: `${API_BASE_URL}/posts`,
    update: (id: string) => `${API_BASE_URL}/posts/${id}`,
    delete: (id: string) => `${API_BASE_URL}/posts/${id}`,
    comments: (id: string) => `${API_BASE_URL}/posts/${id}/comments`,
  },
} as const
```

### API 类型定义 (`src/lib/api/types.ts`)

```typescript
// 通用分页参数
export interface PaginationParams {
  page?: number
  pageSize?: number
  sort?: string
  order?: 'asc' | 'desc'
}

// 通用分页响应
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户相关类型
export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  role: 'admin' | 'user'
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  user: User
  token: string
  refreshToken: string
}

// 文章相关类型
export interface Post {
  id: string
  title: string
  content: string
  authorId: string
  author?: User
  tags: string[]
  status: 'draft' | 'published'
  createdAt: string
  updatedAt: string
}

export interface CreatePostRequest {
  title: string
  content: string
  tags?: string[]
  status?: 'draft' | 'published'
}
```

## 使用示例

### 1. 基础请求

```typescript
import { api } from '@/lib/api/fetch-wrapper'
import { endpoints } from '@/lib/api/endpoints'
import type { User, LoginRequest, LoginResponse } from '@/lib/api/types'

// GET 请求
async function getUsers() {
  try {
    const response = await api.get<User[]>(endpoints.users.list)
    return response.data
  } catch (error) {
    console.error('获取用户列表失败:', error)
    throw error
  }
}

// POST 请求
async function login(credentials: LoginRequest) {
  try {
    const response = await api.post<LoginResponse>(
      endpoints.auth.login,
      credentials
    )
    
    // 保存 token
    localStorage.setItem('auth_token', response.data.token)
    
    return response.data
  } catch (error) {
    console.error('登录失败:', error)
    throw error
  }
}
```

### 2. 带参数的请求

```typescript
// 查询参数
async function getUsersPaginated(params: PaginationParams) {
  const queryString = new URLSearchParams({
    page: params.page?.toString() || '1',
    pageSize: params.pageSize?.toString() || '10',
    ...(params.sort && { sort: params.sort }),
    ...(params.order && { order: params.order }),
  }).toString()
  
  const response = await api.get<PaginatedResponse<User>>(
    `${endpoints.users.list}?${queryString}`
  )
  
  return response.data
}

// 路径参数
async function getUserDetail(userId: string) {
  const response = await api.get<User>(
    endpoints.users.detail(userId)
  )
  
  return response.data
}
```

### 3. 文件上传

```typescript
async function uploadFile(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  const response = await fetchAPI('/api/upload', {
    method: 'POST',
    body: formData,
    headers: {
      // 不设置 Content-Type，让浏览器自动设置
    },
  })
  
  return response.data
}
```

### 4. 取消请求

```typescript
function createCancelableRequest() {
  const controller = new AbortController()
  
  const request = api.get('/api/data', {
    signal: controller.signal,
  })
  
  return {
    request,
    cancel: () => controller.abort(),
  }
}

// 使用
const { request, cancel } = createCancelableRequest()

// 在需要时取消请求
cancel()
```

## 错误处理

### 全局错误处理

```typescript
import { ApiError } from '@/lib/api/fetch-wrapper'

export function handleApiError(error: unknown): string {
  if (error instanceof ApiError) {
    switch (error.code) {
      case 400:
        return '请求参数错误'
      case 401:
        // 跳转到登录页
        window.location.href = '/login'
        return '请先登录'
      case 403:
        return '没有权限访问'
      case 404:
        return '请求的资源不存在'
      case 500:
        return '服务器错误，请稍后重试'
      default:
        return error.message || '请求失败'
    }
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  return '未知错误'
}
```

### 在组件中处理错误

```tsx
import { useState } from 'react'
import { api } from '@/lib/api/fetch-wrapper'
import { handleApiError } from '@/lib/api/error-handler'
import { toast } from 'sonner'

export function UserList() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  
  const fetchUsers = async () => {
    setLoading(true)
    try {
      const response = await api.get<User[]>('/api/users')
      setUsers(response.data)
    } catch (error) {
      const message = handleApiError(error)
      toast.error(message)
    } finally {
      setLoading(false)
    }
  }
  
  // ... 组件其余部分
}
```

## 高级用法

### 1. 自定义拦截器

```typescript
// 添加请求时间戳
const timestampInterceptor = (config: RequestConfig): RequestConfig => {
  const timestamp = Date.now()
  config.headers = {
    ...config.headers,
    'X-Request-Time': timestamp.toString(),
  }
  return config
}

// 处理特定响应
const customResponseInterceptor = async (response: Response) => {
  // 检查自定义头部
  const serverTime = response.headers.get('X-Server-Time')
  if (serverTime) {
    console.log('服务器时间:', serverTime)
  }
  
  return response
}
```

### 2. 请求缓存

```typescript
const cache = new Map<string, { data: any; timestamp: number }>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟

export async function cachedGet<T>(
  url: string,
  options?: RequestConfig
): Promise<T> {
  const cacheKey = `${url}${JSON.stringify(options)}`
  const cached = cache.get(cacheKey)
  
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data
  }
  
  const response = await api.get<T>(url, options)
  cache.set(cacheKey, {
    data: response.data,
    timestamp: Date.now(),
  })
  
  return response.data
}
```

### 3. 请求队列

```typescript
class RequestQueue {
  private queue: Array<() => Promise<any>> = []
  private running = 0
  private maxConcurrent = 3
  
  async add<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await request()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
      
      this.process()
    })
  }
  
  private async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return
    }
    
    this.running++
    const request = this.queue.shift()!
    
    try {
      await request()
    } finally {
      this.running--
      this.process()
    }
  }
}

const requestQueue = new RequestQueue()

// 使用
const data = await requestQueue.add(() => 
  api.get('/api/data')
)
```

## 最佳实践

1. **统一错误处理** - 使用全局错误处理函数处理 API 错误
2. **类型安全** - 为所有 API 请求和响应定义 TypeScript 类型
3. **请求取消** - 在组件卸载时取消未完成的请求
4. **加载状态** - 始终显示请求的加载状态
5. **错误边界** - 使用 React Error Boundary 捕获组件错误
6. **请求去重** - 避免同时发送相同的请求
7. **缓存策略** - 合理使用缓存减少不必要的请求 