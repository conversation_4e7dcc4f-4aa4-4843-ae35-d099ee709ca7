<div data-layer="发现页" className="w-96 h-[1216px] relative bg-Color-背景色-bg1 overflow-hidden">
  <div data-layer="sekai_campaign_past1" className="SekaiCampaignPast1 w-[547px] left-[20px] top-[650px] absolute inline-flex flex-col justify-start items-start gap-1">
    <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-[559px] inline-flex justify-start items-center gap-3 flex-wrap content-center">
      <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-[559px] flex justify-start items-start gap-3 flex-wrap content-start">
        <div data-layer="Auto Layout Vertical" className="AutoLayoutVertical w-96 inline-flex flex-col justify-center items-start gap-0.5">
          <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-96 inline-flex justify-start items-start gap-2.5">
            <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-80 flex justify-start items-start gap-2.5">
              <div data-layer="Your Toxic Ex" className="YourToxicEx w-72 h-5 justify-center text-Color-文本-text1 text-xl font-bold font-['Helvetica_Neue'] leading-normal">Your Toxic Ex</div>
            </div>
            <div data-layer="Vector" className="Vector w-2 h-3.5 bg-Color-文本-text1" />
          </div>
        </div>
        <div data-layer="They cheated on you and they took your dog...." className="TheyCheatedOnYouAndTheyTookYourDog w-80 justify-center text-Color-文本-text6 text-xs font-normal font-['Helvetica_Neue'] leading-none">They cheated on you and they took your dog....</div>
        <div data-layer="Auto Layout Vertical" className="AutoLayoutVertical size- rounded-xl inline-flex flex-col justify-start items-start gap-4">
          <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-40 h-48 px-11 py-14 relative bg-gradient-to-b from-pink-950/0 via-pink-950/90 via 79% to-pink-950/90 rounded-xl inline-flex justify-start items-center gap-2.5">
            <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[111px] top-[6px] absolute bg-Color-蒙层-Mask60/60 rounded-sm flex justify-end items-center gap-0.5">
              <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
                <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
              </div>
              <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">228</div>
            </div>
          </div>
        </div>
        <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-40 h-48 px-11 py-14 relative bg-gradient-to-b from-zinc-500/0 via-zinc-500/90 via 79% to-zinc-500/90 rounded-xl flex justify-start items-center gap-2.5">
          <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[111px] top-[6px] absolute bg-Color-蒙层-Mask60/60 rounded-sm flex justify-end items-center gap-0.5">
            <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
              <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
            </div>
            <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">228</div>
          </div>
        </div>
        <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-40 h-48 px-11 py-14 relative bg-gradient-to-b from-pink-950/0 via-pink-950/90 via 79% to-pink-950/90 rounded-xl flex justify-start items-center gap-2.5">
          <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[111px] top-[6px] absolute bg-Color-蒙层-Mask60/60 rounded-sm flex justify-end items-center gap-0.5">
            <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
              <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
            </div>
            <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">228</div>
          </div>
        </div>
        <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-xs font-normal font-['Helvetica_Neue'] leading-none">Xiao</div>
        <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-xs font-normal font-['Helvetica_Neue'] leading-none">Xiao</div>
        <div data-layer="Chae is autistic and wild i want ..." className="ChaeIsAutisticAndWildIWant w-36 justify-center text-Color-文本-text2 text-[9px] font-normal font-['Helvetica_Neue'] leading-3">Chae is autistic and wild i want ...</div>
        <div data-layer="Chae is autistic and wild i want ..." className="ChaeIsAutisticAndWildIWant w-36 justify-center text-Color-文本-text2 text-[9px] font-normal font-['Helvetica_Neue'] leading-3">Chae is autistic and wild i want ...</div>
        <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-xs font-normal font-['Helvetica_Neue'] leading-3">Xiao</div>
        <div data-layer="Chae is autistic and wild i want ..." className="ChaeIsAutisticAndWildIWant w-36 justify-center text-Color-文本-text4 text-[9px] font-normal font-['Helvetica_Neue'] leading-3">Chae is autistic and wild i want ...</div>
      </div>
    </div>
  </div>
  <div data-layer="sekai_campaign_past2" className="SekaiCampaignPast2 w-[547px] left-[20px] top-[921px] absolute inline-flex flex-col justify-start items-start gap-1">
    <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-[559px] inline-flex justify-start items-center gap-3 flex-wrap content-center">
      <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-[559px] flex justify-start items-start gap-3 flex-wrap content-start">
        <div data-layer="Auto Layout Vertical" className="AutoLayoutVertical w-96 inline-flex flex-col justify-center items-start gap-0.5">
          <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-96 inline-flex justify-start items-start gap-2.5">
            <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-80 flex justify-start items-start gap-2.5">
              <div data-layer="Hot Guys Only" className="HotGuysOnly w-72 h-5 justify-center text-Color-文本-text1 text-xl font-bold font-['Helvetica_Neue'] leading-normal">Hot Guys Only</div>
            </div>
            <div data-layer="Vector" className="Vector w-2 h-3.5 bg-Color-文本-text1" />
          </div>
        </div>
        <div data-layer="Hot guys in your area" className="HotGuysInYourArea w-80 justify-center text-Color-文本-text6 text-[10px] font-normal font-['Inter'] leading-3">Hot guys in your area</div>
        <div data-layer="Auto Layout Vertical" className="AutoLayoutVertical size- rounded-xl inline-flex flex-col justify-start items-start gap-4">
          <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-40 h-48 px-11 py-14 relative bg-gradient-to-b from-indigo-950/0 via-indigo-950/90 via 79% to-indigo-950/90 rounded-xl inline-flex justify-start items-center gap-2.5">
            <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[111px] top-[6px] absolute bg-Color-蒙层-Mask60/60 rounded-sm flex justify-end items-center gap-0.5">
              <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
                <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
              </div>
              <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">228</div>
            </div>
          </div>
        </div>
        <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-40 h-48 px-11 py-14 relative bg-gradient-to-b from-pink-950/0 via-pink-950/90 via 79% to-pink-950/90 rounded-xl flex justify-start items-center gap-2.5">
          <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[111px] top-[6px] absolute bg-Color-蒙层-Mask60/60 rounded-sm flex justify-end items-center gap-0.5">
            <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
              <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
            </div>
            <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">228</div>
          </div>
        </div>
        <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-40 h-48 px-11 py-14 relative bg-gradient-to-b from-pink-950/0 via-pink-950/90 via 79% to-pink-950/90 rounded-xl flex justify-start items-center gap-2.5">
          <div data-layer="Component 1/view count" className="Component1ViewCount size- p-1 left-[111px] top-[6px] absolute bg-Color-蒙层-Mask60/60 rounded-sm flex justify-end items-center gap-0.5">
            <div data-layer="Eye" data-on="On" data-style="Outlined" className="Eye size-3.5 relative overflow-hidden">
              <div data-layer="Shape" className="Shape w-3 h-1.5 left-[1.33px] top-[3.21px] absolute bg-Color-文本-text1" />
            </div>
            <div data-layer="Name" className="Name text-center justify-center text-Color-文本-text1 text-[10px] font-normal font-['Inter'] leading-3">228</div>
          </div>
        </div>
        <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-xs font-normal font-['Helvetica_Neue'] leading-none">Xiao</div>
        <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-xs font-normal font-['Helvetica_Neue'] leading-none">Xiao</div>
        <div data-layer="Chae is autistic and wild i want ..." className="ChaeIsAutisticAndWildIWant w-36 justify-center text-Color-文本-text2 text-[9px] font-normal font-['Helvetica_Neue'] leading-3">Chae is autistic and wild i want ...</div>
        <div data-layer="Chae is autistic and wild i want ..." className="ChaeIsAutisticAndWildIWant w-36 justify-center text-Color-文本-text2 text-[9px] font-normal font-['Helvetica_Neue'] leading-3">Chae is autistic and wild i want ...</div>
        <div data-layer="Xiao" className="Xiao justify-center text-Color-文本-text1 text-xs font-normal font-['Helvetica_Neue'] leading-3">Xiao</div>
        <div data-layer="Chae is autistic and wild i want ..." className="ChaeIsAutisticAndWildIWant w-36 justify-center text-Color-文本-text4 text-[9px] font-normal font-['Helvetica_Neue'] leading-3">Chae is autistic and wild i want ...</div>
      </div>
    </div>
  </div>
  <div data-layer="Fandom" className="Fandom w-[706px] left-[20px] top-[329px] absolute inline-flex flex-col justify-start items-start gap-2">
    <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-96 h-9 inline-flex justify-between items-center">
      <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- flex justify-start items-start gap-2.5">
        <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal size- flex justify-start items-start gap-2.5">
          <div data-layer="🎭 Fandom" className="Fandom text-right justify-center text-Color-文本-text1 text-xl font-bold font-['Helvetica_Neue'] leading-normal">🎭 Fandom</div>
        </div>
      </div>
      <div data-layer="angle-right" className="AngleRight size-5 relative">
        <div data-layer="Vector" className="Vector w-2 h-3.5 left-[6.67px] top-[3.33px] absolute bg-Color-文本-text1" />
      </div>
    </div>
    <div data-layer="Frame 1912055217" className="Frame1912055217 self-stretch inline-flex justify-start items-start gap-2 flex-wrap content-start">
      <div data-layer="Frame" className="Frame w-44 h-28 relative bg-emerald-900 rounded-2xl overflow-hidden">
        <div data-layer="Inyasha" className="Inyasha left-[12px] top-[20px] absolute text-right justify-start text-Color-文本-text1 text-sm font-bold font-['Helvetica_Neue'] leading-none">Inyasha</div>
        <div data-layer="Image" className="Image size-32 left-[59px] top-[39px] absolute" />
      </div>
      <div data-layer="Frame" className="Frame w-44 h-28 relative bg-purple-700 rounded-2xl overflow-hidden">
        <div data-layer="Dragon ball Z" className="DragonBallZ w-16 left-[12px] top-[19px] absolute justify-start text-Color-文本-text1 text-sm font-bold font-['Helvetica_Neue'] leading-none">Dragon ball Z</div>
        <div data-layer="Image" className="Image size-36 left-[67px] top-[26px] absolute" />
        <div data-layer="Frame" className="Frame size-28 left-[69px] top-[19px] absolute overflow-hidden">
          <img data-layer="Image" className="Image w-28 h-40 left-[1px] top-[-13px] absolute" src="https://placehold.co/108x156" />
        </div>
      </div>
      <div data-layer="Frame" className="Frame w-44 h-28 relative bg-violet-400 rounded-2xl overflow-hidden">
        <div data-layer="Dragon ball Z" className="DragonBallZ left-[12px] top-[19px] absolute text-right justify-start text-Color-文本-text1 text-sm font-bold font-['Helvetica_Neue'] leading-none">Dragon ball Z</div>
        <div data-layer="Image" className="Image size-36 left-[67px] top-[26px] absolute" />
        <div data-layer="Style=Filled, Type=Sparkle" data-style="Filled" data-type="Sparkle" className="StyleFilledTypeSparkle size-24 left-[85px] top-[23px] absolute">
          <div data-layer="Vector" className="Vector size-20 left-[5.69px] top-0 absolute bg-blend-luminosity bg-black/10" />
        </div>
      </div>
      <div data-layer="Frame" className="Frame w-44 h-24 relative bg-cyan-700 rounded-2xl overflow-hidden">
        <div data-layer="Doraemon" className="Doraemon left-[12px] top-[20px] absolute text-right justify-start text-Color-文本-text1 text-sm font-bold font-['Helvetica_Neue'] leading-none">Doraemon</div>
        <div data-layer="Image" className="Image size-28 left-[70px] top-[51px] absolute" />
        <img data-layer="Image" className="Image size-20 left-[95px] top-[29px] absolute" src="https://placehold.co/81x81" />
      </div>
      <div data-layer="Frame" className="Frame w-44 h-28 relative bg-cyan-700 rounded-2xl overflow-hidden">
        <div data-layer="Doraemon" className="Doraemon left-[12px] top-[20px] absolute text-right justify-start text-Color-文本-text1 text-sm font-bold font-['Helvetica_Neue'] leading-none">Doraemon</div>
        <div data-layer="Image" className="Image size-28 left-[70px] top-[51px] absolute" />
        <img data-layer="Image" className="Image size-32 left-[68px] top-[29px] absolute" src="https://placehold.co/135x135" />
      </div>
      <div data-layer="Frame" className="Frame w-44 h-28 relative bg-emerald-900 rounded-2xl overflow-hidden">
        <div data-layer="Inyasha" className="Inyasha left-[12px] top-[20px] absolute text-right justify-start text-Color-文本-text1 text-sm font-bold font-['Helvetica_Neue'] leading-none">Inyasha</div>
        <div data-layer="Image" className="Image size-32 left-[59px] top-[39px] absolute" />
        <div data-layer="Frame" className="Frame size-40 left-[63px] top-[34px] absolute overflow-hidden">
          <img data-layer="Image" className="Image size-52 left-[-40px] top-[-9px] absolute" src="https://placehold.co/208x205" />
        </div>
      </div>
      <div data-layer="Frame" className="Frame w-44 h-28 relative bg-purple-700 rounded-2xl overflow-hidden">
        <div data-layer="Dragon ball Z" className="DragonBallZ left-[12px] top-[19px] absolute text-right justify-start text-Color-文本-text1 text-sm font-bold font-['Helvetica_Neue'] leading-none">Dragon ball Z</div>
        <div data-layer="Image" className="Image size-36 left-[67px] top-[26px] absolute" />
        <div data-layer="Frame" className="Frame size-20 left-[95px] top-[43px] absolute overflow-hidden">
          <img data-layer="Image" className="Image w-20 h-28 left-[1px] top-0 absolute" src="https://placehold.co/80x116" />
        </div>
      </div>
      <div data-layer="Frame" className="Frame w-44 h-24 relative bg-violet-400 rounded-2xl overflow-hidden">
        <div data-layer="Dragon ball Z" className="DragonBallZ left-[12px] top-[19px] absolute text-right justify-start text-Color-文本-text1 text-sm font-bold font-['Helvetica_Neue'] leading-none">Dragon ball Z</div>
        <div data-layer="Image" className="Image size-36 left-[67px] top-[26px] absolute" />
        <div data-layer="Style=Filled, Type=Sparkle" data-style="Filled" data-type="Sparkle" className="StyleFilledTypeSparkle size-24 left-[85px] top-[23px] absolute">
          <div data-layer="Vector" className="Vector size-20 left-[5.69px] top-0 absolute bg-blend-luminosity bg-black/10" />
        </div>
      </div>
    </div>
  </div>
  <div data-layer="Search logo" className="SearchLogo size- left-[294px] top-[17px] absolute inline-flex justify-start items-center gap-2">
    <div data-layer="11=no" data-11="no" className="No size-9 relative bg-Color-蒙层-Mask20/20 rounded-[100px]" />
    <div data-layer="Auto Layout Horizontal" className="AutoLayoutHorizontal w-9 p-2 bg-Color-蒙层-Mask20/20 rounded-[100px] flex justify-center items-center gap-2">
      <div data-layer="Outline / Search / Magnifer" className="OutlineSearchMagnifer size-5 relative overflow-hidden">
        <div data-layer="Vector" className="Vector size-4 left-[1.04px] top-[1.04px] absolute bg-Color-文本-text1" />
      </div>
    </div>
  </div>
  <div data-layer="Discover" className="Discover left-[17px] top-[19px] absolute justify-center text-Color-文本-text1 text-3xl font-bold font-['Helvetica_Neue'] leading-loose">Discover</div>
  <div data-layer="Banner" className="Banner w-96 h-60 px-36 py-4 left-[18px] top-[75px] absolute bg-gradient-to-b from-white/0 to-white/10 rounded-xl inline-flex flex-col justify-end items-center gap-2.5">
    <div data-layer="Ellipse 521" className="Ellipse521 size-[5px] bg-white rounded-full" />
    <div data-layer="Ellipse 522" className="Ellipse522 size-[5px] bg-Color-边框-border30/30 rounded-full" />
    <div data-layer="Ellipse 523" className="Ellipse523 size-[5px] bg-Color-边框-border30/30 rounded-full" />
    <div data-layer="Ellipse 524" className="Ellipse524 size-[5px] bg-Color-边框-border30/30 rounded-full" />
  </div>
</div>