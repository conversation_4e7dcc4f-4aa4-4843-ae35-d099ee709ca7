{"name": "h5_discover_page", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "dev:https": "PORT=3001 node server.js", "build": "next build", "start": "next start --port 3001", "start:https": "NODE_ENV=production node server.js", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "lucide-react": "^0.511.0", "next": "15.3.3", "posthog-js": "^1.249.2", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.5", "swiper": "^11.2.8", "tailwind-merge": "^3.3.0", "zod": "^3.25.49", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.3.3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "fs": "0.0.1-security", "https": "^1.0.0", "tailwindcss": "^4", "typescript": "^5"}}