# 多阶段构建 Dockerfile for Next.js 应用 (使用 pnpm - 简化两阶段)

# ========================================
# 阶段 1: 构建阶段  
# ========================================
FROM node:21-alpine AS builder
# (可选) 安装必要的系统依赖，例如 libc6-compat，有时 Next.js 或其依赖在 Alpine 上需要
# RUN apk add --no-cache libc6-compat

# 安装 pnpm
RUN npm install -g pnpm
WORKDIR /app

# 复制包管理文件
COPY package.json pnpm-lock.yaml ./

# 安装所有依赖（包括开发依赖）
RUN pnpm install --frozen-lockfile --ignore-scripts

# 复制源代码
# 确保 .dockerignore 文件配置正确，避免复制不必要的文件 (如 .git, node_modules 本地开发产生的等)
COPY . .

# 构建应用
ENV NEXT_TELEMETRY_DISABLED=1
RUN pnpm run build

# ========================================
# 阶段 2: 运行阶段
# ========================================
FROM node:21-alpine AS runner
WORKDIR /app

# 创建非root用户和组
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 从 builder 阶段复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
# .next/standalone 包含了运行应用所需的最小化 node_modules 和 server.js
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 设置正确的用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV NEXT_TELEMETRY_DISABLED=1

# 启动应用 (server.js 通常在 .next/standalone/server.js)
CMD ["node", "server.js"]
