# 🏗️ Next.js 15 现代化项目结构模板

> 基于 OB-Agent-H5 项目总结的通用架构模板，适用于现代化 Web 应用开发

## 📋 项目概述

这是一个基于 **Next.js 15** + **React 19** + **TypeScript** 的现代化 Web 应用架构模板，集成了状态管理、埋点分析、Flutter 桥接等通用功能模块，使用 **pnpm** 作为依赖管理工具。

## 🏗️ 核心技术栈

### 前端框架
- **Next.js 15** - React 框架 (App Router)
- **React 19** - 用户界面库
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架

### 包管理工具
- **pnpm** - 快速、节省磁盘空间的包管理器

### 状态管理与工具
- **Zustand** - 轻量级状态管理
- **Zod** - 运行时类型验证
- **React Hooks** - 状态逻辑复用

### UI 组件与动画
- **Radix UI** - 无障碍 UI 组件
- **Lucide React** - 图标库
- **Framer Motion** - 动画库
- **Class Variance Authority** - 样式变体管理
- **Sonner** - Toast 通知组件

### 分析与监控
- **PostHog** - 用户行为分析
- **埋点系统** - 自定义事件追踪

### 跨平台集成
- **Flutter Hybrid 容器** - 基于 flutter_inappwebview 的 H5 容器
- **双向消息传递** - Web ↔ Flutter 数据同步

## 📁 项目目录结构

```
project-root/
├── 🌐 public/                          # 静态资源
│   ├── images/                         # 图片资源
│   ├── icons/                          # 图标文件
│   └── [feature]/                     # 功能相关静态资源
├── 🎯 src/                             # 源代码主目录
│   ├── 📱 app/                         # Next.js App Router
│   │   ├── 🧪 (test)/                 # 测试页面组 (开发环境)
│   │   ├── 🔌 api/                    # API 路由
│   │   ├── 📄 layout.tsx              # 全局布局
│   │   └── 🏠 page.tsx                # 首页
│   ├── 🧩 components/                 # React 组件
│   │   ├── 🎨 ui/                     # 基础 UI 组件
│   │   ├── 🌉 hybrid/                 # Hybrid 容器组件
│   │   └── 📋 [feature]/              # 业务功能组件
│   ├── 🪝 hooks/                      # 自定义 React Hooks
│   ├── 📚 lib/                        # 核心业务逻辑
│   │   ├── 🌐 api/                    # API 工具函数
│   │   └── 🛠️ utils/                  # 通用工具函数
│   ├── 🔄 providers/                  # Context Providers
│   ├── 💾 stores/                     # 状态管理
│   └── 📝 types/                      # TypeScript 类型定义
├── ⚙️ 配置文件
│   ├── next.config.ts                 # Next.js 配置
│   ├── tailwind.config.ts             # Tailwind 配置
│   ├── tsconfig.json                  # TypeScript 配置
│   ├── package.json                   # 依赖管理
│   └── pnpm-lock.yaml                 # pnpm 锁文件
└── 📋 文档文件
    ├── README.md                      # 项目说明
    └── docs/                          # 详细文档目录
```

## 🧩 核心功能模块

> **重要提示**：以下功能按优先级排序，Flutter 桥接和 PostHog 埋点涉及跨团队协作，建议优先集成。

### 1. 🌉 Flutter Hybrid 容器系统 ⭐️ 高优先级
实现 Web 与 Flutter 的双向通信能力，是与移动端团队协作的关键基础设施。

👉 详细文档：[docs/flutter-bridge.md](docs/flutter-bridge.md)

### 2. 📊 埋点分析系统 ⭐️ 高优先级
基于 PostHog 的用户行为分析和事件追踪，需要与数据团队对齐埋点规范。

👉 详细文档：[docs/analytics.md](docs/analytics.md)

### 3. 💾 状态管理系统
使用 Zustand 实现的轻量级状态管理方案。

👉 详细文档：[docs/state-management.md](docs/state-management.md)

### 4. 🌐 API 请求封装
统一的网络请求处理和错误管理。

👉 详细文档：[docs/api-integration.md](docs/api-integration.md)

### 5. 🎨 UI 组件系统
基于 Radix UI 和 CVA 构建的可访问性组件库。

👉 详细文档：[docs/ui-components.md](docs/ui-components.md)

### 6. 🪝 通用 Hooks
常用的自定义 React Hooks 集合。

👉 详细文档：[docs/custom-hooks.md](docs/custom-hooks.md)

### 7. 🛠️ 工具函数
项目中的通用工具函数库。

👉 详细文档：[docs/utils.md](docs/utils.md)

## ⚙️ 配置文件

项目配置文件的详细说明和模板。

👉 详细文档：[docs/configuration.md](docs/configuration.md)

## 📦 依赖管理

使用 pnpm 进行高效的依赖管理。

👉 详细文档：[docs/pnpm-guide.md](docs/pnpm-guide.md)

## 🚀 快速开始

### 1. 项目初始化

```bash
# 使用 pnpm 创建 Next.js 项目
pnpm create next-app@latest your-project-name --typescript --tailwind --eslint --app --use-pnpm

# 进入项目目录
cd your-project-name

# 安装依赖
pnpm install
```

### 2. 优先集成关键功能

#### 集成 Flutter 桥接（第一优先级）
```bash
# 1. 创建 Flutter 桥接 Provider
mkdir -p src/providers
# 参考 docs/flutter-bridge.md 实现

# 2. 在根布局中配置
# 3. 与移动端团队对接测试
```

#### 集成 PostHog 埋点（第二优先级）
```bash
# 1. 安装 PostHog
pnpm add posthog-js

# 2. 配置环境变量
NEXT_PUBLIC_POSTHOG_KEY=your_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# 3. 创建 Analytics Provider
# 参考 docs/analytics.md 实现

# 4. 与数据团队对齐埋点规范
```

### 3. 环境配置

```bash
# 复制环境变量模板
cp env.example .env.local

# 编辑环境变量
vim .env.local
```

### 4. 开发运行

```bash
# 启动开发服务器
pnpm dev

# 构建项目
pnpm build

# 启动生产服务器
pnpm start
```

## 🎯 最佳实践

### 代码组织
- **功能模块化** - 按功能而非类型组织代码
- **类型安全** - 充分利用 TypeScript 类型系统
- **组件复用** - 构建可复用的 UI 组件库
- **状态管理** - 合理划分全局和局部状态

### 性能优化
- **代码分割** - 使用动态导入和 React.lazy
- **图片优化** - 使用 Next.js Image 组件
- **缓存策略** - 合理使用 SWR 或 React Query
- **Bundle 分析** - 定期分析打包体积

### 开发体验
- **类型提示** - 完善的 TypeScript 类型定义
- **开发工具** - 集成 ESLint、Prettier
- **文档维护** - 保持文档与代码同步
- **代码规范** - 统一的编码风格

## 📋 项目检查清单

### 关键集成（优先完成）
- [ ] Flutter Hybrid 容器配置 ⭐️
- [ ] PostHog 埋点集成 ⭐️
- [ ] 与移动端团队完成桥接联调
- [ ] 与数据团队对齐埋点规范

### 基础设置
- [ ] Next.js 15 + React 19 配置
- [ ] TypeScript 配置和路径别名
- [ ] Tailwind CSS 和主题配置
- [ ] ESLint 和 Prettier 配置
- [ ] pnpm 作为包管理器

### 核心功能
- [ ] 状态管理 (Zustand)
- [ ] API 请求封装
- [ ] 错误处理机制
- [ ] UI 组件系统 (Radix UI + CVA)
- [ ] Toast 通知系统

### 部署准备
- [ ] 构建优化设置
- [ ] Docker 配置 (可选)
- [ ] CI/CD 流水线 (可选)
- [ ] 生产环境配置

## 📝 总结

这个模板提供了一个完整的现代化 Web 应用架构基础，具有以下特点：

- **现代技术栈** - 基于 Next.js 15 和 React 19 的最新特性
- **类型安全** - 完整的 TypeScript 支持
- **高效包管理** - 使用 pnpm 提升依赖安装速度和磁盘利用率
- **组件化开发** - 基于 Radix UI 的可访问性组件系统
- **状态管理** - 轻量级的 Zustand 状态管理方案
- **跨平台支持** - 集成 Flutter Hybrid 容器，支持移动端集成
- **开发体验** - 完善的开发工具链和最佳实践

可以根据具体项目需求进行调整和扩展，快速搭建高质量的 Web 应用。 