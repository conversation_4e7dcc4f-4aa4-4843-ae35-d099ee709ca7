import type { NextConfig } from "next";
import fs from 'fs';
import path from 'path';

const nextConfig: NextConfig = {
  // 输出配置 - 启用 standalone 模式用于 Docker 部署
  output: 'standalone',

  // 开发环境配置
  ...(process.env.NODE_ENV === 'development' && {
    // 配置自定义服务器选项
    experimental: {
      // 允许自定义服务器
    }
  }),
  
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'stage-data.sekai.chat',
        port: '',
        pathname: '/**', // 允许此主机下的所有路径
      },
    ],
  },
  
  // 其他配置选项
  async rewrites() {
    return [];
  },
  
  // 配置主机名
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
