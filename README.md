# H5 Discover Page

Flutter 桥接和 PostHog 分析测试平台

## 功能特性

### Flutter Bridge 测试
- 🔧 设备信息获取
- 🧭 导航控制
- 💾 本地存储操作
- 📤 系统分享功能
- 🔐 权限管理
- 📱 Toast 消息
- 📳 设备震动
- 📋 剪贴板操作

### PostHog 分析测试
- 👤 用户识别与管理
- 📊 自定义事件追踪
- 🚩 功能标志管理
- 👥 用户属性设置
- 🎥 会话录制
- 📈 分析仪表板

## 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **UI 组件**: Radix UI + shadcn/ui
- **状态管理**: Zustand
- **分析**: PostHog
- **工具**: pnpm, ESLint

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 环境配置

1. 复制环境变量示例文件：
```bash
cp env.example .env.local
```

2. 配置 PostHog（可选）：
```env
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

### 启动开发服务器

```bash
pnpm dev
```

访问 http://localhost:3001

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── (test)/            # 测试页面组
│   │   ├── flutter-bridge/ # Flutter 桥接测试
│   │   └── posthog/       # PostHog 测试
│   └── api/               # API 路由
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   └── hybrid/           # 混合应用组件
├── lib/                  # 工具库
│   ├── flutter-bridge.ts # Flutter 桥接实现
│   └── posthog.ts       # PostHog 配置
├── types/               # TypeScript 类型定义
├── hooks/               # React Hooks
├── stores/              # Zustand 状态管理
└── providers/           # React Context Providers
```

## Flutter 集成

在 Flutter WebView 中加载此应用时，需要注入 JavaScript 接口：

```dart
webViewController.addJavaScriptChannel(
  'flutter',
  onMessageReceived: (JavaScriptMessage message) {
    // 处理来自 H5 的消息
  },
);
```

## 开发指南

### 添加新的 Flutter 桥接方法

1. 在 `src/types/flutter.ts` 中定义类型
2. 在 `src/lib/flutter-bridge.ts` 中实现方法
3. 在测试页面中添加测试用例

### 添加 PostHog 事件

```typescript
import { posthog } from '@/lib/posthog';

// 发送自定义事件
posthog.capture('event_name', {
  property1: 'value1',
  property2: 'value2'
});
```

## 部署

### Vercel 部署

```bash
vercel
```

### Docker 部署

```bash
docker build -t h5-discover-page .
docker run -p 3001:3001 h5-discover-page
```

## 许可证

MIT
