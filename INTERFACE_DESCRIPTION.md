# 界面描述文档

## 项目概述

本项目是一个H5版本的Discover（发现）页面，使用Next.js 15开发。主要包含发现页主页面和点击后进入的二级详情页面。该页面将通过H5容器嵌入到原生应用中，底部导航栏由原生应用提供。所有与运营工具相关的功能不在本次开发范围内。

## API 接口

### 1. 获取 Sekai 信息

-   **URL**: `http://127.0.0.1:7000/sekai/getOuterStreamInfo`
-   **Method**: `GET`
-   **Query Params**:
    -   `sekai_ids`: string (逗号分隔的 Sekai ID 列表, e.g., "1600,2018")
-   **Success Response (200)**:
    ```json
    {
        "code": 200,
        "success": true,
        "data": [
            {
                "sekaiId": 1600,
                "newCoverUrl": "https://stage-data.sekai.chat/aiu-background/aiu-universe-cover/1600/a2a2bba4-c1bb-4e10-bea7-45c02a835594.large.webp?AWSAccessKeyId=AKIAQE43KJDN7ARTLAVM&Signature=tmo4%2FZRSOiaFOgYA2rZRc7HFLak%3D&Expires=1749133123",
                "creatorUserId": 0,
                "creatorUserName": "",
                "title": "Olivia - Childhood Companion",
                "isPublic": true,
                "intro": "Description here...",
                "category": [
                    {
                        "id": 1,
                        "label_name": "boyfriend",
                        "display_name": "Boyfriend",
                        "emoji": "👨"
                    }
                ],
                "viewCount": 0,
                "templateId": 0,
                "coverVideo": "",
                "multiPlayerCoverflag": false
            }
        ],
        "message": "Success"
    }
    ```

### 2. 获取 Banner 或往期运营内容

-   **URL**: `http://localhost:7700/api/v1/discover/page/config/by-type`
-   **Method**: `GET`
-   **Query Params**:
    -   `type`: string (e.g., "sekai_campaign_banner", "sekai_campaign_past")
    -   `page`: number (e.g., 1)
    -   `size`: number (e.g., 10)
-   **Success Response (0)**:
    ```json
    {
        "code": 0,
        "msg": "success",
        "data": {
            "total": 2,
            "page": 1,
            "size": 10,
            "pages": 1,
            "items": [
                {
                    "id": 1,
                    "type": "sekai_campaign_banner", // or "sekai_campaign_past"
                    "data": {
                        "title": "夏日炎炎！泳装特别活动！",
                        "subtitle": "参与活动获取限定角色",
                        "cover_image_url": "https://stage-data.sekai.chat/aiu-audio/test/hfZ6QtNo0puiIz8AUGys.png",
                        "secondary_page_image_url": "https://stage-data.sekai.chat/aiu-audio/test/hfZ6QtNo0puiIz8AUGys.png",
                        "linked_sekai_ids": [ 101, 102, 105 ]
                    },
                    "status": "active",
                    "sort_order": 10,
                    "created_at": 1749045069,
                    "updated_at": 1749045069
                }
            ]
        }
    }
    ```

### 3. 获取 Fandom 信息

-   **URL**: `http://127.0.0.1:7000/tags/v2/listTags`
-   **Method**: `GET`
-   **Query Params**:
    -   `page`: number (e.g., 1)
    -   `size`: number (e.g., 10)
    -   `tagScenario`: string (e.g., "browse")
-   **Success Response (200)**:
    ```json
    {
        "code": 200,
        "success": true,
        "data": {
            "items": [
                {
                    "id": -1,
                    "displayName": "Created by you",
                    "tagType": 0,
                    "creationType": 2,
                    "extra": {
                        "backgroundColor": 29617,
                        "charCount": 52,
                        "charPose": "https://stage-data.sekai.chat/aiu-character/0/6151abaa-d18b-4c49-adb3-48bb8a7d8a17.large.webp?AWSAccessKeyId=AKIAQE43KJDN7ARTLAVM&Signature=00p%2FxjbpoZt%2BBBlnJLy9McgL%2BEw%3D&Expires=1749098133"
                    }
                },
                {
                    "id": 36,
                    "displayName": "Black Clover",
                    "tagType": 0,
                    "creationType": 2,
                    "extra": {
                        "coverUrl": "",
                        "description": "A magicless boy strives...",
                        "weight": 536,
                        "charCount": 24,
                        "charPose": "https://stage-data.sekai.chat/aiu-character/0/6151abaa-d18b-4c49-adb3-48bb8a7d8a17.large.webp?AWSAccessKeyId=AKIAQE43KJDN7ARTLAVM&Signature=00p%2FxjbpoZt%2BBBlnJLy9McgL%2BEw%3D&Expires=1749098133",
                        "backgroundColor": 3563058
                    }
                }
            ],
            "total": 534,
            "page": 1,
            "size": 10,
            "pages": 54
        },
        "message": ""
    }
    ```

## 数据结构定义 (TypeScript)

将根据上述 API 响应在 `src/lib/api/types.ts` 中定义。